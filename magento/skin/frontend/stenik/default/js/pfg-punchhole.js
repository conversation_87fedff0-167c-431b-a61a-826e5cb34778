/**
 * PFG Punch Hole Caching JavaScript Framework
 * Handles AJAX loading of dynamic content for CDN caching
 * 
 * @category   PFG
 * @package    PFG_PunchHole
 */

var PFGPunchHole = {
    
    // Configuration
    config: {
        baseUrl: '',
        debug: true, // Enable debug for testing
        retryAttempts: 3,
        retryDelay: 1000,
        cacheTimeout: 300000 // 5 minutes
    },

    // Cache for AJAX responses
    cache: {},

    // Active AJAX requests
    activeRequests: {},

    // Flag to prevent multiple section loads
    sectionsLoaded: false,
    
    /**
     * Initialize the punch hole system
     */
    init: function(baseUrl, debug, config) {
        this.config.baseUrl = baseUrl || '';
        this.config.debug = debug || false;

        // Override default config with passed config
        if (config) {
            for (var key in config) {
                if (config.hasOwnProperty(key)) {
                    this.config[key] = config[key];
                }
            }
        }

        // Wait for DOM ready
        if (typeof jQuery !== 'undefined') {
            jQuery(document).ready(function() {
                PFGPunchHole.loadAllSections();
                PFGPunchHole.initProductPage();
                PFGPunchHole.bindEvents();
            });
        } else {
            // Fallback for vanilla JS
            document.addEventListener('DOMContentLoaded', function() {
                PFGPunchHole.loadAllSections();
                PFGPunchHole.initProductPage();
                PFGPunchHole.bindEvents();
            });
        }
    },

    /**
     * Initialize product page if we're on a product page
     */
    initProductPage: function() {
        var self = this;

        // Add a small delay to ensure DOM is fully loaded
        setTimeout(function() {
            // Check if we're on a product page by looking for product ID
            var productIdElement = document.querySelector('input[name="product"]');
            if (self.config.debug) {
                console.log('PFGPunchHole: Looking for product ID element:', productIdElement);
            }

            if (productIdElement) {
                var productId = productIdElement.value;
                if (productId) {
                    if (self.config.debug) {
                        console.log('PFGPunchHole: Detected product page, loading product data for ID:', productId);
                        console.log('PFGPunchHole: Checking for buy button element:', document.querySelector('.addToCartBtn, .btn-cart'));
                    }
                    self.loadProductPage(productId);
                } else {
                    if (self.config.debug) {
                        console.log('PFGPunchHole: Product ID element found but no value');
                    }
                }
            } else {
                if (self.config.debug) {
                    console.log('PFGPunchHole: Not a product page, skipping product page initialization');
                }
            }
        }, 100);
    },
    
    /**
     * Load all dynamic sections with a single AJAX request (OPTIMIZED)
     */
    loadAllSections: function() {
        // Prevent multiple loads
        if (this.sectionsLoaded) {
            if (this.config.debug) {
                console.log('PFGPunchHole: Sections already loaded, skipping');
            }
            return;
        }

        var self = this;
        var cacheKey = 'allSections';

        // Check cache first
        if (this.cache[cacheKey] && (Date.now() - this.cache[cacheKey].timestamp) < this.config.cacheTimeout) {
            if (this.config.debug) {
                console.log('PFGPunchHole: Using cached data for all sections');
            }
            this.updateAllSections(this.cache[cacheKey].data);
            this.sectionsLoaded = true;
            return;
        }

        var url = this.config.baseUrl + 'punchhole/ajax/allsections'; // Lowercase for Magento routing

        if (this.config.debug) {
            console.log('PFGPunchHole: Loading all sections from:', url);
        }

        this.makeRequest(url, function(data) {
            if (data && data.success) {
                // Cache the response
                self.cache[cacheKey] = {
                    data: data,
                    timestamp: Date.now()
                };

                // Update all sections
                self.updateAllSections(data);

                // Mark sections as loaded
                self.sectionsLoaded = true;

                if (self.config.debug) {
                    console.log('PFGPunchHole: All sections loaded successfully');
                }
            } else {
                if (self.config.debug) {
                    console.error('PFGPunchHole: Failed to load all sections:', data);
                }
            }
        }, function(error) {
            if (self.config.debug) {
                console.error('PFGPunchHole: Error loading all sections:', error);
            }
        });
    },

    /**
     * Update all sections with consolidated data
     */
    updateAllSections: function(data) {
        if (data.headerCustomer) {
            this.updateHeaderCustomer(data.headerCustomer);
        }
        if (data.headerCart) {
            this.updateHeaderCart(data.headerCart);
        }
        if (data.responsiveCustomer) {
            this.updateResponsiveCustomer(data.responsiveCustomer);
        }
        if (data.responsiveCart) {
            this.updateResponsiveCart(data.responsiveCart);
        }
    },

    /**
     * Load product page dynamic content with a single AJAX call (OPTIMIZED)
     */
    loadProductPage: function(productId) {
        if (!productId) {
            if (this.config.debug) {
                console.error('PFGPunchHole: Product ID is required for loadProductPage');
            }
            return;
        }

        var self = this;
        var cacheKey = 'productPage_' + productId;

        // Check cache first
        if (this.cache[cacheKey] && (Date.now() - this.cache[cacheKey].timestamp) < this.config.cacheTimeout) {
            if (this.config.debug) {
                console.log('PFGPunchHole: Using cached data for product page:', productId);
            }
            this.updateProductPage(this.cache[cacheKey].data);
            return;
        }

        var url = this.config.baseUrl + 'punchhole/ajax/productpage?product_id=' + productId;

        if (this.config.debug) {
            console.log('PFGPunchHole: Loading product page data from:', url);
        }

        this.makeRequest(url, function(data) {
            if (data && data.success) {
                // Cache the response
                self.cache[cacheKey] = {
                    data: data,
                    timestamp: Date.now()
                };

                // Update product page sections
                self.updateProductPage(data);

                if (self.config.debug) {
                    console.log('PFGPunchHole: Product page data loaded successfully for product:', productId);
                }
            } else {
                if (self.config.debug) {
                    console.error('PFGPunchHole: Failed to load product page data:', data);
                }
            }
        }, function(error) {
            if (self.config.debug) {
                console.error('PFGPunchHole: Error loading product page data:', error);
            }
        });
    },

    /**
     * Update product page sections with consolidated data
     */
    updateProductPage: function(data) {
        if (data.addToCart) {
            this.updateAddToCart(data.addToCart);
        }
        if (data.addToWishlist) {
            this.updateAddToWishlist(data.addToWishlist);
        }
        if (data.addToCompare) {
            this.updateAddToCompare(data.addToCompare);
        }
        if (data.leasing) {
            this.updateLeasing(data.leasing);
        }
        if (data.personalizedLabel) {
            this.updatePersonalizedLabel(data.personalizedLabel);
        }
    },

    /**
     * Load a specific dynamic section
     */
    loadSection: function(section, forceReload) {
        var self = this;
        
        // Check cache first
        if (!forceReload && this.cache[section] && this.cache[section].timestamp > (Date.now() - this.config.cacheTimeout)) {
            this.updateSection(section, this.cache[section].data);
            return;
        }
        
        // Prevent duplicate requests
        if (this.activeRequests[section]) {
            return;
        }
        
        var url = this.config.baseUrl + 'punchhole/ajax/' + section;
        
        this.log('Loading section: ' + section + ' from ' + url);
        
        // Make AJAX request
        this.activeRequests[section] = this.makeRequest(url, {
            success: function(data) {
                delete self.activeRequests[section];
                
                if (data.success) {
                    // Cache the response
                    self.cache[section] = {
                        data: data,
                        timestamp: Date.now()
                    };
                    
                    self.updateSection(section, data);
                } else {
                    self.log('Error loading section ' + section + ': ' + (data.error || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                delete self.activeRequests[section];
                self.log('AJAX error loading section ' + section + ': ' + error);
                
                // Retry logic
                self.retrySection(section, 1);
            }
        });
    },
    
    /**
     * Retry loading a section with exponential backoff
     */
    retrySection: function(section, attempt) {
        var self = this;
        
        if (attempt <= this.config.retryAttempts) {
            var delay = this.config.retryDelay * Math.pow(2, attempt - 1);
            
            this.log('Retrying section ' + section + ' (attempt ' + attempt + ') in ' + delay + 'ms');
            
            setTimeout(function() {
                self.loadSection(section, true);
            }, delay);
        } else {
            this.log('Failed to load section ' + section + ' after ' + this.config.retryAttempts + ' attempts');
        }
    },
    
    /**
     * Update DOM with section data
     */
    updateSection: function(section, data) {
        switch (section) {
            case 'headerCustomer':
                this.updateHeaderCustomer(data);
                break;
            case 'headerCart':
                this.updateHeaderCart(data);
                break;
            case 'responsiveCustomer':
                this.updateResponsiveCustomer(data);
                break;
            case 'responsiveCart':
                this.updateResponsiveCart(data);
                break;
        }
    },
    
    /**
     * Update header customer section (.headerRight)
     */
    updateHeaderCustomer: function(data) {
        var container = this.getElement('.headerRight');
        if (!container) return;
        
        var html = '';
        
        // Compare link
        html += '<a href="' + data.compare.url + '" class="compare" title="' + data.compare.label + '">';
        html += data.compare.label;
        if (data.compare.count > 0) {
            html += '<span class="number">' + data.compare.count + '</span>';
        }
        html += '</a>';
        
        // Wishlist link
        html += '<a href="' + data.wishlist.url + '" class="wishlist' + (data.wishlist.hasItems ? ' hasItems' : '') + '" title="' + data.wishlist.label + '">';
        html += data.wishlist.label;
        if (data.wishlist.hasItems) {
            html += '<span class="number">' + data.wishlist.count + '</span>';
        }
        html += '</a>';
        
        // Login/Profile link
        if (data.customer.isLoggedIn) {
            html += '<a href="' + data.customer.profileUrl + '" class="login loggedIn" title="' + data.customer.profileLabel + '">';
            html += data.customer.profileLabel + '</a>';
        } else {
            html += '<a href="' + data.customer.loginUrl + '" class="login" title="' + data.customer.loginLabel + '">';
            html += data.customer.loginLabel + '</a>';
        }
        
        container.innerHTML = html;
        this.log('Updated headerCustomer section');
    },
    
    /**
     * Update header cart section
     */
    updateHeaderCart: function(data) {
        var container = this.getElement('.headerCart');
        if (!container) return;
        
        // Update cart link
        container.href = data.cart.url;
        container.className = 'headerCart' + (data.cart.hasItems ? ' hasItems' : '');
        
        // Update cart count and text
        var numberSpan = container.querySelector('.number');
        var textNode = container.lastChild;
        
        if (numberSpan) {
            numberSpan.textContent = data.cart.hasItems ? data.cart.cartQty : '0';
        }
        
        // Update product/products text
        if (textNode && textNode.nodeType === 3) {
            textNode.textContent = data.cart.productLabel;
        }
        
        this.log('Updated headerCart section');
    },
    
    /**
     * Update responsive customer section
     */
    updateResponsiveCustomer: function(data) {
        var container = this.getElement('.responsiveMenuSub .accountLinks');
        if (!container) return;
        
        var html = '';
        
        if (data.customer.isLoggedIn) {
            html += '<a href="' + data.customer.profileUrl + '">' + data.customer.profileLabel + '</a>';
            html += '<span class="sep">/</span>';
            html += '<a href="' + data.customer.logoutUrl + '">' + data.customer.logoutLabel + '</a>';
        } else {
            html += '<a href="' + data.customer.loginUrl + '">' + data.customer.loginLabel + '</a>';
            html += '<span class="sep">/</span>';
            html += '<a href="' + data.customer.registerUrl + '">' + data.customer.registerLabel + '</a>';
        }
        
        container.innerHTML = html;
        
        // Update wishlist
        var wishlistLink = this.getElement('.responsiveMenuSub .wishtlistItems');
        if (wishlistLink) {
            wishlistLink.href = data.wishlist.url;
            wishlistLink.className = 'wishtlistItems' + (data.wishlist.hasItems ? ' hasItems' : '');
            
            var wishlistCount = wishlistLink.querySelector('.responsiveWishlistitems');
            if (wishlistCount) {
                wishlistCount.innerHTML = ' - ' + data.wishlist.count + ' ' + data.wishlist.addedLabel;
            }
        }
        
        // Update compare
        var compareLink = this.getElement('.responsiveMenuSub .responsiveCompare');
        if (compareLink) {
            compareLink.href = data.compare.url;
            
            var compareNumber = compareLink.querySelector('.number');
            if (compareNumber) {
                compareNumber.textContent = data.compare.count;
                compareNumber.style.display = data.compare.count > 0 ? 'inline' : 'none';
            }
        }
        
        this.log('Updated responsiveCustomer section');
    },
    
    /**
     * Update responsive cart section
     */
    updateResponsiveCart: function(data) {
        var container = this.getElement('.responsiveCart');
        if (!container) return;
        
        container.href = data.cart.url;
        container.className = 'responsiveCart' + (data.cart.hasItems ? ' hasItems' : '');
        
        var countSpan = container.querySelector('.responsiveCartitems');
        if (countSpan) {
            countSpan.textContent = data.cart.itemCount;
        }
        
        this.log('Updated responsiveCart section');
    },
    
    /**
     * Bind events for dynamic updates
     */
    bindEvents: function() {
        var self = this;
        
        // Listen for cart updates
        if (typeof jQuery !== 'undefined') {
            jQuery(document).on('ajaxComplete', function(event, xhr, settings) {
                // Check if this was a cart-related AJAX call
                if (settings.url && (settings.url.indexOf('checkout') !== -1 || settings.url.indexOf('cart') !== -1)) {
                    // Reload cart sections after a short delay
                    setTimeout(function() {
                        self.loadSection('headerCart', true);
                        self.loadSection('responsiveCart', true);
                    }, 500);
                }
            });
        }
        
        // Listen for customer login/logout events
        this.bindCustomerEvents();
    },
    
    /**
     * Bind customer-related events
     */
    bindCustomerEvents: function() {
        var self = this;
        
        // Monitor for login/logout by checking for redirects to customer pages
        var currentUrl = window.location.href;
        
        // If we're on a customer page, reload customer sections
        if (currentUrl.indexOf('customer/account') !== -1) {
            setTimeout(function() {
                self.loadSection('headerCustomer', true);
                self.loadSection('responsiveCustomer', true);
            }, 1000);
        }
    },

    /**
     * Update Add to Cart section
     */
    updateAddToCart: function(data) {
        if (this.config.debug) {
            console.log('PFGPunchHole: updateAddToCart called with data:', data);
        }

        var addToCartBtn = document.querySelector('.addToCartBtn, .btn-cart');
        if (this.config.debug) {
            console.log('PFGPunchHole: Found buy button element:', addToCartBtn);
        }

        if (addToCartBtn) {
            if (data.isSaleable && !data.isGrouped) {
                if (this.config.debug) {
                    console.log('PFGPunchHole: Product is saleable, showing buy button');
                }
                addToCartBtn.style.display = 'inline-block';
                addToCartBtn.innerHTML = data.buyNowLabel || 'Buy now';
            } else {
                if (this.config.debug) {
                    console.log('PFGPunchHole: Product not saleable or is grouped, hiding buy button');
                }
                addToCartBtn.style.display = 'none';
            }
        } else {
            if (this.config.debug) {
                console.log('PFGPunchHole: Buy button element not found!');
            }
        }

        // Update form key if present
        var formKeyInput = document.querySelector('input[name="form_key"]');
        if (formKeyInput && data.formKey) {
            formKeyInput.value = data.formKey;
        }
    },

    /**
     * Update Add to Wishlist section
     */
    updateAddToWishlist: function(data) {
        var wishlistLink = document.querySelector('.addToWishlist, .link-wishlist');
        if (wishlistLink) {
            if (data.isAllowed) {
                wishlistLink.style.display = 'inline-block';
                wishlistLink.href = data.url;
                wishlistLink.innerHTML = data.label || 'Add to wishlist';

                if (data.isInWishlist) {
                    wishlistLink.classList.add('in-wishlist');
                    wishlistLink.innerHTML = 'In Wishlist';
                }
            } else {
                wishlistLink.style.display = 'none';
            }
        }
    },

    /**
     * Update Add to Compare section
     */
    updateAddToCompare: function(data) {
        var compareLink = document.querySelector('.addToCompareist, .link-compare');
        if (compareLink) {
            compareLink.href = data.url;
            compareLink.innerHTML = data.label || 'Add to compare list';

            if (data.isInCompare) {
                compareLink.classList.add('in-compare');
                compareLink.innerHTML = 'In Compare';
            }
        }
    },

    /**
     * Update Leasing section
     */
    updateLeasing: function(data) {
        var leasingButton = document.querySelector('.buyOnLeasingButton');
        var leasingTab = document.querySelector('.leasingTabLink');

        if (data.isAvailable) {
            if (leasingButton) {
                leasingButton.style.display = 'inline-block';
                leasingButton.innerHTML = data.buttonLabel || 'Buy on leasing';
            }
            if (leasingTab) {
                leasingTab.style.display = 'inline-block';
                leasingTab.innerHTML = data.tabLabel || 'Buy on leasing';
            }
        } else {
            if (leasingButton) leasingButton.style.display = 'none';
            if (leasingTab) leasingTab.style.display = 'none';
        }
    },

    /**
     * Update Personalized Label section
     */
    updatePersonalizedLabel: function(data) {
        var personalizedSection = document.querySelector('.personalized-label-section');
        if (personalizedSection) {
            if (data.isAvailable) {
                personalizedSection.style.display = 'block';

                // Update form key
                var formKeyInput = personalizedSection.querySelector('input[name="form_key"]');
                if (formKeyInput && data.formKey) {
                    formKeyInput.value = data.formKey;
                }

                // Update labels
                var wishInput = personalizedSection.querySelector('#stenik_personalizedlabel_wish');
                if (wishInput && data.wishLabel) {
                    wishInput.placeholder = data.wishLabel + ' *';
                }

                var fontSelect = personalizedSection.querySelector('#stenik_personalizedlabel_font option[value=""]');
                if (fontSelect && data.fontSelectionLabel) {
                    fontSelect.innerHTML = data.fontSelectionLabel + ' *';
                }

                var addButton = personalizedSection.querySelector('#product-addtocart-button-personalizedlabel');
                if (addButton && data.addLabel) {
                    addButton.innerHTML = data.addLabel;
                }
            } else {
                personalizedSection.style.display = 'none';
            }
        }
    },

    /**
     * Make AJAX request (jQuery or vanilla JS)
     */
    makeRequest: function(url, options) {
        if (typeof jQuery !== 'undefined') {
            return jQuery.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                success: options.success,
                error: options.error
            });
        } else {
            // Vanilla JS fallback
            var xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var data = JSON.parse(xhr.responseText);
                            options.success(data);
                        } catch (e) {
                            options.error(xhr, 'parsererror', e.message);
                        }
                    } else {
                        options.error(xhr, 'error', 'HTTP ' + xhr.status);
                    }
                }
            };
            
            xhr.send();
            return xhr;
        }
    },
    
    /**
     * Get DOM element (jQuery or vanilla JS)
     */
    getElement: function(selector) {
        if (typeof jQuery !== 'undefined') {
            var $el = jQuery(selector);
            return $el.length > 0 ? $el[0] : null;
        } else {
            return document.querySelector(selector);
        }
    },
    
    /**
     * Log debug messages
     */
    log: function(message) {
        if (this.config.debug && typeof console !== 'undefined') {
            console.log('[PFG PunchHole] ' + message);
        }
    },
    
    /**
     * Public method to refresh all sections
     */
    refresh: function() {
        this.cache = {}; // Clear cache
        this.loadAllSections();
    },
    
    /**
     * Public method to refresh specific section
     */
    refreshSection: function(section) {
        delete this.cache[section]; // Clear section cache
        this.loadSection(section, true);
    }
};
