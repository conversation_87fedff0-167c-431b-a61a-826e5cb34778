<?php

/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2022 Amasty (https://www.amasty.com)
 * @package Product Feed
 */
class Amasty_Feed_MainController extends Mage_Core_Controller_Front_Action
{
    /**
     * exclude file extension from name, and lookup profile
     */
    public function downloadAction()
    {
        $fileName = $this->_sanitizeFileName($this->getRequest()->getParam('file'));
        try {           
            $file = str_replace(array('.csv', '.xml', '.txt'), '', $fileName);
            $this->_download($file);
        } catch (Exception $e) {
            try {
                //compatibility with primary generated file names, versions before  3.3.4 - July 24, 2016
                $filePath = Mage::helper('amfeed')->getDownloadPath('feeds', $fileName);
                $this->_prepareDownloadResponse($fileName, array(
                    'value' => $filePath,
                    'type' => 'filename'
                ));
            } catch (Exception $e) {
                Mage::logException($e, null, 'amfeed.log');
                $this->_forward('noRoute');
            }
        }
    }

    /**
     * lookup profile by filename and download
     */
    // public function getAction()
    // {
    //     try
    //     {

    //         $fileName = $this->getRequest()->getParam('file'); 
    //        //  Mage::log("DEBUG: getAction() reached with file = {$fileName}", null, 'custom_debug.log', true);           
    //          $brand = $this->getRequest()->getParam('brand'); // e.g., "Casio"

    //         Mage::log("DEBUG: getAction() reached with file = {$fileName}, brand = {$brand}", null, 'custom_debug.log', true);
    //         $this->_download($fileName);
    //     } catch (Exception $e) {
    //         $this->_forward('noRoute');
    //     }
    // }

    public function getAction()
    {
        try {
            $fileName = $this->getRequest()->getParam('file');
            $brand = $this->getRequest()->getParam('brand');
           
            // Check if we need to filter by brand
            if ($brand) {
                $this->_filteredDownload($fileName, $brand);
            } else {
                $this->_download($fileName);
            }
        } catch (Exception $e) {
            Mage::log('Exception in getAction(): ' . $e->getMessage(), null, 'custom_debug.log', true);
            $this->_forward('noRoute');
        }
    }

    /**
     * @param $fileName
     * @return Mage_Core_Controller_Varien_Action
     */
    protected function _download($fileName)
    {
        try {
            $filePath = $this->_locateFeedFile($fileName);
        } catch (Exception $e) {
            Mage::log($e->getMessage(), null, 'amfeed_https.log', true);
            return $this->_forward('noRoute');
        }

        /* choose MIME type by extension */
        $ext  = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        $mime = array('csv' => 'text/csv', 'xml' => 'application/xml', 'txt' => 'text/plain');
        $this->_prepareDownloadResponse(
            basename($filePath),
            array('type' => 'filename', 'value' => $filePath, 'rm' => false),
            isset($mime[$ext]) ? $mime[$ext] : 'application/octet-stream'
        );
    }

    /**
     * Stream an XML feed filtered by brand.
     */
    protected function _filteredDownload($fileName, $brand)
    {
        try {
            $filePath = $this->_locateFeedFile($fileName);   // helper we added earlier
        } catch (Exception $e) {
            Mage::log($e->getMessage(), null, 'amfeed_https.log', true);
            return $this->_forward('noRoute');
        }

        if (strtolower(pathinfo($filePath, PATHINFO_EXTENSION)) !== 'xml') {
            Mage::throwException('Brand-filtered download expects an XML feed.');
        }

        if (!is_readable($filePath)) {
            Mage::log("Feed file not readable: {$filePath}", null, 'amfeed_https.log', true);
            return $this->_forward('noRoute');
        }

        $xml = simplexml_load_file($filePath, 'SimpleXMLElement', LIBXML_NOCDATA);
        if ($xml === false) {
            Mage::throwException('Unable to parse feed XML.');
        }

        if ($brand) {
            foreach ($xml->channel->item as $idx => $item) {
                $itemBrand = (string)$item->brand;
                if (strcasecmp($itemBrand, $brand) !== 0) {
                    unset($xml->channel->item[$idx]);         // remove non-matching items
                }
            }
        }


        $output = preg_replace('#http://#i', 'https://', $xml->asXML());
        $downloadName = $fileName . '_' . $brand . '.xml';

        $this->getResponse()
            ->setHttpResponseCode(200)
            ->setHeader('Pragma',              'public', true)
            ->setHeader('Cache-Control',       'must-revalidate, post-check=0, pre-check=0', true)
            ->setHeader('Content-Type',        'application/xml', true)
            ->setHeader('Content-Length',      strlen($output), true)
            ->setHeader('Content-Disposition', 'attachment; filename="' . $downloadName . '"', true)
            ->setBody($output);

        Mage::log("Filtered download served: {$downloadName}", null, 'amfeed_https.log', true);
    }


    /**
     * Locate the feed file in media/amfeed/feeds/ regardless of extension.
     * Returns absolute path or throws an exception.
     */
    private function _locateFeedFile($baseName)
    {
        $feedDir = Mage::getBaseDir('media') . DS . 'amfeed' . DS . 'feeds';

        if (!is_dir($feedDir)) {
            throw new Mage_Core_Exception("Feed directory not found: {$feedDir}");
        }

        foreach (scandir($feedDir) as $entry) {
            if (stripos($entry, $baseName . '.') === 0) {       // ❶ starts with gads_duplicate.
                return $feedDir . DS . $entry;                  // → gads_duplicate.csv / xml / txt …
            }
        }

        throw new Mage_Core_Exception("Feed file not found for base name: {$baseName}");
    }


    /**
     * @param $filename
     * @return mixed
     */
    protected static function _sanitizeFileName($filename)
    {
        $chars = array(" ", '"', "'", "&", "/", "\\", "?", "#");

        // every forbidden character is replace by an underscore
        return str_replace($chars, '_', $filename);
    }
}
