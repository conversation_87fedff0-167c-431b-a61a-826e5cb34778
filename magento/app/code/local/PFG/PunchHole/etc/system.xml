<?xml version="1.0"?>
<!--
/**
 * PFG Punch Hole System Configuration
 * 
 * @category   PFG
 * @package    PFG_PunchHole
 */
-->
<config>
    <tabs>
        <pfg translate="label" module="pfg_punchhole">
            <label>PFG</label>
            <sort_order>200</sort_order>
        </pfg>
    </tabs>
    
    <sections>
        <pfg_punchhole translate="label" module="pfg_punchhole">
            <label>Punch Hole Caching</label>
            <tab>pfg</tab>
            <frontend_type>text</frontend_type>
            <sort_order>100</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>1</show_in_website>
            <show_in_store>1</show_in_store>
            
            <groups>
                <general translate="label">
                    <label>General Settings</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>10</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    
                    <fields>
                        <enabled translate="label">
                            <label>Enable Punch Hole Caching</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Enable AJAX loading of dynamic content for CDN caching</comment>
                        </enabled>
                        
                        <debug translate="label">
                            <label>Debug Mode</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Enable debug logging in browser console</comment>
                        </debug>
                        
                        <cache_timeout translate="label">
                            <label>Cache Timeout (seconds)</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>How long to cache AJAX responses in browser (default: 300)</comment>
                        </cache_timeout>
                        
                        <retry_attempts translate="label">
                            <label>Retry Attempts</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Number of retry attempts for failed AJAX requests (default: 3)</comment>
                        </retry_attempts>
                    </fields>
                </general>
                
                <sections translate="label">
                    <label>Dynamic Sections</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>20</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    
                    <fields>
                        <header_customer translate="label">
                            <label>Header Customer Section</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Enable AJAX loading for header customer section (compare, wishlist, login)</comment>
                        </header_customer>
                        
                        <header_cart translate="label">
                            <label>Header Cart Section</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Enable AJAX loading for header cart section</comment>
                        </header_cart>
                        
                        <responsive_customer translate="label">
                            <label>Responsive Customer Section</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Enable AJAX loading for responsive customer section</comment>
                        </responsive_customer>
                        
                        <responsive_cart translate="label">
                            <label>Responsive Cart Section</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Enable AJAX loading for responsive cart section</comment>
                        </responsive_cart>
                    </fields>
                </sections>
            </groups>
        </pfg_punchhole>
    </sections>
</config>
