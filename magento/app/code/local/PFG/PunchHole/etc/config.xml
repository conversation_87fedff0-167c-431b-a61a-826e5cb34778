<?xml version="1.0"?>
<!--
/**
 * PFG Punch Hole Caching Module Configuration
 * 
 * @category   PFG
 * @package    PFG_PunchHole
 */
-->
<config>
    <modules>
        <PFG_PunchHole>
            <version>1.0.0</version>
        </PFG_PunchHole>
    </modules>
    
    <frontend>
        <routers>
            <pfg_punchhole>
                <use>standard</use>
                <args>
                    <module>PFG_PunchHole</module>
                    <frontName>punchhole</frontName>
                </args>
            </pfg_punchhole>
        </routers>
        <layout>
            <updates>
                <pfg_punchhole>
                    <file>pfg_punchhole.xml</file>
                </pfg_punchhole>
            </updates>
        </layout>
    </frontend>
    
    <global>
        <models>
            <pfg_punchhole>
                <class>PFG_PunchHole_Model</class>
            </pfg_punchhole>
        </models>
        
        <blocks>
            <pfg_punchhole>
                <class>PFG_PunchHole_Block</class>
            </pfg_punchhole>
        </blocks>
        
        <helpers>
            <pfg_punchhole>
                <class>PFG_PunchHole_Helper</class>
            </pfg_punchhole>
        </helpers>
    </global>
</config>
