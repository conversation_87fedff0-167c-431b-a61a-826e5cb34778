<?php
/**
 * PFG Punch Hole Helper
 * 
 * @category   PFG
 * @package    PFG_PunchHole
 */

class PFG_PunchHole_Helper_Data extends Mage_Core_Helper_Abstract
{
    /**
     * Check if punch hole caching is enabled
     * 
     * @return bool
     */
    public function isEnabled()
    {
        return Mage::getStoreConfigFlag('pfg_punchhole/general/enabled');
    }
    
    /**
     * Get AJAX endpoint URL
     * 
     * @param string $action
     * @return string
     */
    public function getAjaxUrl($action)
    {
        return Mage::getUrl('punchhole/ajax/' . $action);
    }
    
    /**
     * Get cache key for dynamic content
     * 
     * @param string $section
     * @return string
     */
    public function getCacheKey($section)
    {
        $customerId = Mage::getSingleton('customer/session')->getCustomerId();
        $storeId = Mage::app()->getStore()->getId();
        
        return 'pfg_punchhole_' . $section . '_' . $storeId . '_' . ($customerId ?: 'guest');
    }
    
    /**
     * Check if current request should be cached
     * 
     * @return bool
     */
    public function shouldCache()
    {
        // Don't cache AJAX requests
        if ($this->_getRequest()->isAjax()) {
            return false;
        }
        
        // Don't cache admin pages
        if (Mage::app()->getStore()->isAdmin()) {
            return false;
        }
        
        // Don't cache checkout pages
        $currentRoute = $this->_getRequest()->getRouteName();
        $excludedRoutes = array('checkout', 'customer', 'wishlist');
        
        if (in_array($currentRoute, $excludedRoutes)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Generate placeholder HTML for dynamic content
     * 
     * @param string $section
     * @param array $attributes
     * @return string
     */
    public function getPlaceholder($section, $attributes = array())
    {
        $defaultAttributes = array(
            'class' => 'punchhole-placeholder',
            'data-section' => $section,
            'data-url' => $this->getAjaxUrl($section)
        );
        
        $attributes = array_merge($defaultAttributes, $attributes);
        
        $attributeString = '';
        foreach ($attributes as $key => $value) {
            $attributeString .= ' ' . $key . '="' . htmlspecialchars($value) . '"';
        }
        
        return '<div' . $attributeString . '></div>';
    }
}
