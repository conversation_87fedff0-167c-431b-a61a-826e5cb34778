<?php
/**
 * PFG Punch Hole Index Controller
 * Simple test controller to verify routing works
 * 
 * @category   PFG
 * @package    PFG_PunchHole
 * <AUTHOR> Development Team
 */
class PFG_PunchHole_IndexController extends Mage_Core_Controller_Front_Action
{
    /**
     * Default index action
     */
    public function indexAction()
    {
        $this->getResponse()->setHeader('Content-Type', 'text/plain');
        $this->getResponse()->setBody('PFG PunchHole Index Controller is working!');
    }
    
    /**
     * Simple test action
     */
    public function testAction()
    {
        $this->getResponse()->setHeader('Content-Type', 'text/plain');
        $this->getResponse()->setBody('PFG PunchHole Test Action is working!');
    }
}
