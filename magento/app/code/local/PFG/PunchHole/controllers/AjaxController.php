<?php
/**
 * PFG Punch Hole AJAX Controller
 * Provides JSON/HTML endpoints for dynamic content sections
 * 
 * @category   PFG
 * @package    PFG_PunchHole
 */

class PFG_PunchHole_AjaxController extends Mage_Core_Controller_Front_Action
{

    /**
     * Get all dynamic content in a single request (OPTIMIZED)
     * This replaces multiple individual AJAX calls with one consolidated call
     */
    public function allSectionsAction()
    {


        try {
            $helper = Mage::helper('pfg_punchhole');

            // Get compare products data
            $compareHelper = Mage::helper('catalog/product_compare');
            $compareCount = $compareHelper->getItemCount();
            $compareUrl = $compareCount > 0 ? $compareHelper->getListUrl() : 'javascript:;';

            // Get wishlist data
            $wishlistHelper = Mage::helper('wishlist');
            $wishlistCount = $wishlistHelper->getItemCount();
            $wishlistUrl = Mage::getUrl('wishlist');

            // Get customer data
            $customerHelper = Mage::helper('customer');
            $isLoggedIn = $customerHelper->isLoggedIn();

            // Get cart data
            $cartHelper = Mage::helper('checkout/cart');
            $cartItemCount = (int)$cartHelper->getItemsCount();
            $cartQty = (int)$cartHelper->getItemsQty(); // Fixed method name for Magento 1.9
            $cartUrl = Mage::getUrl('checkout/cart');

            $response = array(
                'success' => true,
                'headerCustomer' => array(
                    'compare' => array(
                        'count' => $compareCount,
                        'url' => $compareUrl,
                        'label' => $helper->__('Compare')
                    ),
                    'wishlist' => array(
                        'count' => $wishlistCount,
                        'url' => $wishlistUrl,
                        'label' => $helper->__('Wishlist'),
                        'hasItems' => $wishlistCount >= 1
                    ),
                    'customer' => array(
                        'isLoggedIn' => $isLoggedIn,
                        'loginUrl' => Mage::getUrl('customer/account/login'),
                        'profileUrl' => Mage::getUrl('customer/account'),
                        'loginLabel' => $helper->__('Login'),
                        'profileLabel' => $helper->__('Profile')
                    )
                ),
                'headerCart' => array(
                    'itemCount' => $cartItemCount,
                    'cartQty' => $cartQty,
                    'url' => $cartUrl,
                    'hasItems' => $cartItemCount > 0,
                    'productLabel' => $cartItemCount == 1 ? $helper->__('product') : $helper->__('products')
                ),
                'responsiveCustomer' => array(
                    'customer' => array(
                        'isLoggedIn' => $isLoggedIn,
                        'profileUrl' => Mage::getUrl('customer/account'),
                        'loginUrl' => Mage::getUrl('customer/account/login'),
                        'logoutUrl' => Mage::getUrl('customer/account/logout'),
                        'registerUrl' => Mage::getUrl('customer/account/create'),
                        'profileLabel' => $helper->__('Profile'),
                        'loginLabel' => $helper->__('Login'),
                        'logoutLabel' => $helper->__('Logout'),
                        'registerLabel' => $helper->__('Registration')
                    ),
                    'wishlist' => array(
                        'count' => $wishlistCount,
                        'url' => $wishlistUrl,
                        'label' => $helper->__('My Wishlist'),
                        'addedLabel' => $helper->__('added'),
                        'hasItems' => $wishlistCount >= 1
                    ),
                    'compare' => array(
                        'count' => $compareCount,
                        'url' => $compareUrl,
                        'label' => $helper->__('Compare')
                    )
                ),
                'responsiveCart' => array(
                    'itemCount' => $cartItemCount,
                    'url' => $cartUrl,
                    'hasItems' => $cartItemCount > 0
                )
            );

        } catch (Exception $e) {
            $response = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            Mage::logException($e);
        }

        $this->getResponse()->setHeader('Content-Type', 'application/json');
        $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($response));
    }

    /**
     * Get product page dynamic content in a single request (OPTIMIZED)
     * Returns user-specific product interactions: cart, wishlist, compare, leasing, personalized label
     *
     * @return void
     */
    public function productPageAction()
    {


        try {
            $productId = (int)$this->getRequest()->getParam('product_id');
            if (!$productId) {
                $this->getResponse()->setHeader('Content-Type', 'application/json');
                $this->getResponse()->setBody(Mage::helper('core')->jsonEncode(array(
                    'success' => false,
                    'error' => 'Product ID is required'
                )));
                return;
            }

            // Load product
            $product = Mage::getModel('catalog/product')->load($productId);
            if (!$product->getId()) {
                throw new Exception('Product not found');
            }

            // Get customer session data
            $customer = Mage::getSingleton('customer/session');
            $isLoggedIn = $customer->isLoggedIn();

            // Get compare and wishlist counts
            $compareCount = (int)Mage::helper('catalog/product_compare')->getItemCount();
            $wishlistCount = 0;
            if ($isLoggedIn) {
                $wishlistCount = (int)Mage::helper('wishlist')->getItemCount();
            }

            // Check if product is in wishlist or compare
            $isInWishlist = false;
            $isInCompare = false;
            if ($isLoggedIn) {
                $wishlist = Mage::getModel('wishlist/wishlist')->loadByCustomer($customer->getCustomer());
                $wishlistItems = $wishlist->getItemCollection();
                foreach ($wishlistItems as $item) {
                    if ($item->getProductId() == $productId) {
                        $isInWishlist = true;
                        break;
                    }
                }
            }

            // Check if product is in compare list - use visitor session approach
            $compareList = Mage::getSingleton('catalog/product_compare_list');
            $compareProductIds = $compareList->getItemCollection()->getAllIds();
            $isInCompare = in_array($productId, $compareProductIds);

            // Get product URLs
            $wishlistUrl = Mage::helper('wishlist')->getAddUrl($product);
            $compareUrl = Mage::helper('catalog/product_compare')->getAddUrl($product);

            // Check leasing availability
            $isLeasingAvailable = false;
            $leasingProviders = array();

            if (Mage::helper('core')->isModuleEnabled('Stenik_LeasingUniCredit') &&
                Mage::helper('stenik_leasingunicredit/product')->isLeasingAvailable($product, true)) {
                $isLeasingAvailable = true;
                $leasingProviders[] = 'unicredit';
            }

            if (Mage::helper('core')->isModuleEnabled('Stenik_LeasingTbi') &&
                Mage::helper('stenik_leasingtbi/product')->isLeasingAvailable($product, true)) {
                $isLeasingAvailable = true;
                $leasingProviders[] = 'tbi';
            }

            // Check personalized label (engraving) availability
            $hasPersonalizedLabel = Mage::helper('core')->isModuleEnabled('Stenik_PersonalizedLabel');

            // Prepare consolidated response
            $response = array(
                'success' => true,
                'productId' => $productId,
                'addToCart' => array(
                    'isSaleable' => $product->isSaleable(),
                    'isGrouped' => $product->isGrouped(),
                    'submitUrl' => Mage::getUrl('checkout/cart/add'),
                    'formKey' => Mage::getSingleton('core/session')->getFormKey(),
                    'buyNowLabel' => $this->__('Buy now')
                ),
                'addToWishlist' => array(
                    'isAllowed' => Mage::helper('wishlist')->isAllow(),
                    'isInWishlist' => $isInWishlist,
                    'url' => $wishlistUrl,
                    'count' => $wishlistCount,
                    'label' => $this->__('Add to wishlist')
                ),
                'addToCompare' => array(
                    'url' => $compareUrl,
                    'isInCompare' => $isInCompare,
                    'count' => $compareCount,
                    'label' => $this->__('Add to compate list')
                ),
                'leasing' => array(
                    'isAvailable' => $isLeasingAvailable,
                    'providers' => $leasingProviders,
                    'tabLabel' => $this->__('Buy on leasing'),
                    'buttonLabel' => $this->__('Buy on leasing')
                ),
                'personalizedLabel' => array(
                    'isAvailable' => $hasPersonalizedLabel,
                    'submitUrl' => $product->getProductUrl(),
                    'formKey' => Mage::getSingleton('core/session')->getFormKey(),
                    'wishLabel' => $this->__('Wish:'),
                    'fontSelectionLabel' => $this->__('Font Selection'),
                    'addLabel' => $this->__('Add')
                )
            );

            $this->getResponse()->setHeader('Content-Type', 'application/json');
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($response));

        } catch (Exception $e) {
            Mage::logException($e);
            $this->getResponse()->setHeader('Content-Type', 'application/json');
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode(array(
                'success' => false,
                'error' => $e->getMessage()
            )));
        }
    }

    /**
     * Get header customer content (headerRight section)
     * Returns: compare count, wishlist count, login/profile status
     * @deprecated Use allSectionsAction() instead for better performance
     */
    public function headerCustomerAction()
    {
        try {
            $helper = Mage::helper('pfg_punchhole');
            
            // Get compare products data
            $compareHelper = Mage::helper('catalog/product_compare');
            $compareCount = $compareHelper->getItemCount();
            $compareUrl = $compareCount > 0 ? $compareHelper->getListUrl() : 'javascript:;';
            
            // Get wishlist data
            $wishlistHelper = Mage::helper('wishlist');
            $wishlistCount = $wishlistHelper->getItemCount();
            $wishlistUrl = Mage::getUrl('wishlist');
            
            // Get customer data
            $customerHelper = Mage::helper('customer');
            $isLoggedIn = $customerHelper->isLoggedIn();
            $loginUrl = Mage::getUrl('customer/account/login');
            $profileUrl = Mage::getUrl('customer/account');
            
            $response = array(
                'success' => true,
                'compare' => array(
                    'count' => $compareCount,
                    'url' => $compareUrl,
                    'label' => $helper->__('Compare')
                ),
                'wishlist' => array(
                    'count' => $wishlistCount,
                    'url' => $wishlistUrl,
                    'label' => $helper->__('Wishlist'),
                    'hasItems' => $wishlistCount >= 1
                ),
                'customer' => array(
                    'isLoggedIn' => $isLoggedIn,
                    'loginUrl' => $loginUrl,
                    'profileUrl' => $profileUrl,
                    'loginLabel' => $helper->__('Login'),
                    'profileLabel' => $helper->__('Profile')
                )
            );
            
        } catch (Exception $e) {
            $response = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            Mage::logException($e);
        }
        
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(Mage::helper('core')->jsonEncode($response));
    }
    
    /**
     * Get header cart content
     * Returns: cart item count, cart URL, product/products text
     */
    public function headerCartAction()
    {
        try {
            $helper = Mage::helper('pfg_punchhole');
            $cartHelper = Mage::helper('checkout/cart');
            
            // Get cart data
            $itemCount = (int)count($cartHelper->getCart()->getItems());
            $cartQty = (int)$cartHelper->getSummaryCount();
            $cartUrl = Mage::getUrl('checkout/cart');
            
            $response = array(
                'success' => true,
                'cart' => array(
                    'itemCount' => $itemCount,
                    'cartQty' => $cartQty,
                    'url' => $cartUrl,
                    'hasItems' => $itemCount > 0,
                    'productLabel' => $itemCount == 1 ? $helper->__('product') : $helper->__('products')
                )
            );
            
        } catch (Exception $e) {
            $response = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            Mage::logException($e);
        }
        
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(Mage::helper('core')->jsonEncode($response));
    }
    
    /**
     * Get responsive header customer content
     * Returns: login/logout links, wishlist count, compare count
     */
    public function responsiveCustomerAction()
    {
        try {
            $helper = Mage::helper('pfg_punchhole');
            
            // Get customer data
            $customerHelper = Mage::helper('customer');
            $isLoggedIn = $customerHelper->isLoggedIn();
            
            // Get compare products data
            $compareHelper = Mage::helper('catalog/product_compare');
            $compareCount = $compareHelper->getItemCount();
            $compareUrl = $compareCount > 0 ? $compareHelper->getListUrl() : 'javascript:;';
            
            // Get wishlist data
            $wishlistHelper = Mage::helper('wishlist');
            $wishlistCount = $wishlistHelper->getItemCount();
            $wishlistUrl = Mage::getUrl('wishlist');
            
            $response = array(
                'success' => true,
                'customer' => array(
                    'isLoggedIn' => $isLoggedIn,
                    'profileUrl' => Mage::getUrl('customer/account'),
                    'loginUrl' => Mage::getUrl('customer/account/login'),
                    'logoutUrl' => Mage::getUrl('customer/account/logout'),
                    'registerUrl' => Mage::getUrl('customer/account/create'),
                    'profileLabel' => $helper->__('Profile'),
                    'loginLabel' => $helper->__('Login'),
                    'logoutLabel' => $helper->__('Logout'),
                    'registerLabel' => $helper->__('Registration')
                ),
                'wishlist' => array(
                    'count' => $wishlistCount,
                    'url' => $wishlistUrl,
                    'label' => $helper->__('My Wishlist'),
                    'addedLabel' => $helper->__('added'),
                    'hasItems' => $wishlistCount >= 1
                ),
                'compare' => array(
                    'count' => $compareCount,
                    'url' => $compareUrl,
                    'label' => $helper->__('Compare')
                )
            );
            
        } catch (Exception $e) {
            $response = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            Mage::logException($e);
        }
        
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(Mage::helper('core')->jsonEncode($response));
    }
    
    /**
     * Get responsive cart data for mobile header
     * Returns: cart item count for responsive cart icon
     */
    public function responsiveCartAction()
    {
        try {
            $cartHelper = Mage::helper('checkout/cart');
            $itemCount = (int)$cartHelper->getItemsQty();
            $cartUrl = Mage::getUrl('checkout/cart');
            
            $response = array(
                'success' => true,
                'cart' => array(
                    'itemCount' => $itemCount,
                    'url' => $cartUrl,
                    'hasItems' => $itemCount >= 1
                )
            );
            
        } catch (Exception $e) {
            $response = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            Mage::logException($e);
        }
        
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(Mage::helper('core')->jsonEncode($response));
    }
}
