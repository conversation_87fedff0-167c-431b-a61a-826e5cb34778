<?php
/**
 * PFG Punch Hole AJAX Controller
 * Provides JSON/HTML endpoints for dynamic content sections
 * 
 * @category   PFG
 * @package    PFG_PunchHole
 */

class PFG_PunchHole_AjaxController extends Mage_Core_Controller_Front_Action
{
    /**
     * Get all dynamic content in a single request (OPTIMIZED)
     * This replaces multiple individual AJAX calls with one consolidated call
     */
    public function allSectionsAction()
    {
        try {
            $helper = Mage::helper('pfg_punchhole');

            // Get compare products data
            $compareHelper = Mage::helper('catalog/product_compare');
            $compareCount = $compareHelper->getItemCount();
            $compareUrl = $compareCount > 0 ? $compareHelper->getListUrl() : 'javascript:;';

            // Get wishlist data
            $wishlistHelper = Mage::helper('wishlist');
            $wishlistCount = $wishlistHelper->getItemCount();
            $wishlistUrl = Mage::getUrl('wishlist');

            // Get customer data
            $customerHelper = Mage::helper('customer');
            $isLoggedIn = $customerHelper->isLoggedIn();

            // Get cart data
            $cartHelper = Mage::helper('checkout/cart');
            $cartItemCount = (int)$cartHelper->getItemsCount();
            $cartQty = (int)$cartHelper->getItemsQty(); // Fixed method name for Magento 1.9
            $cartUrl = Mage::getUrl('checkout/cart');

            $response = array(
                'success' => true,
                'headerCustomer' => array(
                    'compare' => array(
                        'count' => $compareCount,
                        'url' => $compareUrl,
                        'label' => $helper->__('Compare')
                    ),
                    'wishlist' => array(
                        'count' => $wishlistCount,
                        'url' => $wishlistUrl,
                        'label' => $helper->__('Wishlist'),
                        'hasItems' => $wishlistCount >= 1
                    ),
                    'customer' => array(
                        'isLoggedIn' => $isLoggedIn,
                        'loginUrl' => Mage::getUrl('customer/account/login'),
                        'profileUrl' => Mage::getUrl('customer/account'),
                        'loginLabel' => $helper->__('Login'),
                        'profileLabel' => $helper->__('Profile')
                    )
                ),
                'headerCart' => array(
                    'itemCount' => $cartItemCount,
                    'cartQty' => $cartQty,
                    'url' => $cartUrl,
                    'hasItems' => $cartItemCount > 0,
                    'productLabel' => $cartItemCount == 1 ? $helper->__('product') : $helper->__('products')
                ),
                'responsiveCustomer' => array(
                    'customer' => array(
                        'isLoggedIn' => $isLoggedIn,
                        'profileUrl' => Mage::getUrl('customer/account'),
                        'loginUrl' => Mage::getUrl('customer/account/login'),
                        'logoutUrl' => Mage::getUrl('customer/account/logout'),
                        'registerUrl' => Mage::getUrl('customer/account/create'),
                        'profileLabel' => $helper->__('Profile'),
                        'loginLabel' => $helper->__('Login'),
                        'logoutLabel' => $helper->__('Logout'),
                        'registerLabel' => $helper->__('Registration')
                    ),
                    'wishlist' => array(
                        'count' => $wishlistCount,
                        'url' => $wishlistUrl,
                        'label' => $helper->__('My Wishlist'),
                        'addedLabel' => $helper->__('added'),
                        'hasItems' => $wishlistCount >= 1
                    ),
                    'compare' => array(
                        'count' => $compareCount,
                        'url' => $compareUrl,
                        'label' => $helper->__('Compare')
                    )
                ),
                'responsiveCart' => array(
                    'itemCount' => $cartItemCount,
                    'url' => $cartUrl,
                    'hasItems' => $cartItemCount > 0
                )
            );

        } catch (Exception $e) {
            $response = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            Mage::logException($e);
        }

        $this->getResponse()->setHeader('Content-Type', 'application/json');
        $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($response));
    }

    /**
     * Get header customer content (headerRight section)
     * Returns: compare count, wishlist count, login/profile status
     * @deprecated Use allSectionsAction() instead for better performance
     */
    public function headerCustomerAction()
    {
        try {
            $helper = Mage::helper('pfg_punchhole');
            
            // Get compare products data
            $compareHelper = Mage::helper('catalog/product_compare');
            $compareCount = $compareHelper->getItemCount();
            $compareUrl = $compareCount > 0 ? $compareHelper->getListUrl() : 'javascript:;';
            
            // Get wishlist data
            $wishlistHelper = Mage::helper('wishlist');
            $wishlistCount = $wishlistHelper->getItemCount();
            $wishlistUrl = Mage::getUrl('wishlist');
            
            // Get customer data
            $customerHelper = Mage::helper('customer');
            $isLoggedIn = $customerHelper->isLoggedIn();
            $loginUrl = Mage::getUrl('customer/account/login');
            $profileUrl = Mage::getUrl('customer/account');
            
            $response = array(
                'success' => true,
                'compare' => array(
                    'count' => $compareCount,
                    'url' => $compareUrl,
                    'label' => $helper->__('Compare')
                ),
                'wishlist' => array(
                    'count' => $wishlistCount,
                    'url' => $wishlistUrl,
                    'label' => $helper->__('Wishlist'),
                    'hasItems' => $wishlistCount >= 1
                ),
                'customer' => array(
                    'isLoggedIn' => $isLoggedIn,
                    'loginUrl' => $loginUrl,
                    'profileUrl' => $profileUrl,
                    'loginLabel' => $helper->__('Login'),
                    'profileLabel' => $helper->__('Profile')
                )
            );
            
        } catch (Exception $e) {
            $response = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            Mage::logException($e);
        }
        
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(Mage::helper('core')->jsonEncode($response));
    }
    
    /**
     * Get header cart content
     * Returns: cart item count, cart URL, product/products text
     */
    public function headerCartAction()
    {
        try {
            $helper = Mage::helper('pfg_punchhole');
            $cartHelper = Mage::helper('checkout/cart');
            
            // Get cart data
            $itemCount = (int)count($cartHelper->getCart()->getItems());
            $cartQty = (int)$cartHelper->getSummaryCount();
            $cartUrl = Mage::getUrl('checkout/cart');
            
            $response = array(
                'success' => true,
                'cart' => array(
                    'itemCount' => $itemCount,
                    'cartQty' => $cartQty,
                    'url' => $cartUrl,
                    'hasItems' => $itemCount > 0,
                    'productLabel' => $itemCount == 1 ? $helper->__('product') : $helper->__('products')
                )
            );
            
        } catch (Exception $e) {
            $response = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            Mage::logException($e);
        }
        
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(Mage::helper('core')->jsonEncode($response));
    }
    
    /**
     * Get responsive header customer content
     * Returns: login/logout links, wishlist count, compare count
     */
    public function responsiveCustomerAction()
    {
        try {
            $helper = Mage::helper('pfg_punchhole');
            
            // Get customer data
            $customerHelper = Mage::helper('customer');
            $isLoggedIn = $customerHelper->isLoggedIn();
            
            // Get compare products data
            $compareHelper = Mage::helper('catalog/product_compare');
            $compareCount = $compareHelper->getItemCount();
            $compareUrl = $compareCount > 0 ? $compareHelper->getListUrl() : 'javascript:;';
            
            // Get wishlist data
            $wishlistHelper = Mage::helper('wishlist');
            $wishlistCount = $wishlistHelper->getItemCount();
            $wishlistUrl = Mage::getUrl('wishlist');
            
            $response = array(
                'success' => true,
                'customer' => array(
                    'isLoggedIn' => $isLoggedIn,
                    'profileUrl' => Mage::getUrl('customer/account'),
                    'loginUrl' => Mage::getUrl('customer/account/login'),
                    'logoutUrl' => Mage::getUrl('customer/account/logout'),
                    'registerUrl' => Mage::getUrl('customer/account/create'),
                    'profileLabel' => $helper->__('Profile'),
                    'loginLabel' => $helper->__('Login'),
                    'logoutLabel' => $helper->__('Logout'),
                    'registerLabel' => $helper->__('Registration')
                ),
                'wishlist' => array(
                    'count' => $wishlistCount,
                    'url' => $wishlistUrl,
                    'label' => $helper->__('My Wishlist'),
                    'addedLabel' => $helper->__('added'),
                    'hasItems' => $wishlistCount >= 1
                ),
                'compare' => array(
                    'count' => $compareCount,
                    'url' => $compareUrl,
                    'label' => $helper->__('Compare')
                )
            );
            
        } catch (Exception $e) {
            $response = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            Mage::logException($e);
        }
        
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(Mage::helper('core')->jsonEncode($response));
    }
    
    /**
     * Get responsive cart data for mobile header
     * Returns: cart item count for responsive cart icon
     */
    public function responsiveCartAction()
    {
        try {
            $cartHelper = Mage::helper('checkout/cart');
            $itemCount = (int)$cartHelper->getItemsQty();
            $cartUrl = Mage::getUrl('checkout/cart');
            
            $response = array(
                'success' => true,
                'cart' => array(
                    'itemCount' => $itemCount,
                    'url' => $cartUrl,
                    'hasItems' => $itemCount >= 1
                )
            );
            
        } catch (Exception $e) {
            $response = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            Mage::logException($e);
        }
        
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(Mage::helper('core')->jsonEncode($response));
    }
}
