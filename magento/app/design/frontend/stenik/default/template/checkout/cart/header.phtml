<?php
/**
 * Header Cart Section - Punch Hole Caching
 * This template now renders a placeholder that will be populated via AJAX
 * to enable full page caching while maintaining dynamic cart content
 *
 * @package  Stenik_Template
 * <AUTHOR> <<EMAIL>>
 * @see      Mage_Checkout_Block_Cart_Sidebar
 */
?>

<?php if ($this->getIsNeedToDisplaySideBar()): ?>
    <!-- Placeholder content for CDN caching - will be replaced by AJAX -->
    <a href="<?php echo Mage::getUrl('checkout/cart') ?>" class="headerCart" id="header-cart-placeholder">
        <span class="number">0</span>
        <?php echo $this->__('products');?>
    </a>

    <!-- PFG Punch Hole: Content loaded via consolidated AJAX call from header-customer.phtml -->


<?php endif ?>
