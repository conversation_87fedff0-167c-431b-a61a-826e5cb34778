<?php
/**
 * Header Cart Section - Punch Hole Caching
 * This template now renders a placeholder that will be populated via AJAX
 * to enable full page caching while maintaining dynamic cart content
 *
 * @package  Stenik_Template
 * <AUTHOR> <<EMAIL>>
 * @see      Mage_Checkout_Block_Cart_Sidebar
 */
?>

<?php if ($this->getIsNeedToDisplaySideBar()): ?>
    <!-- Placeholder content for CDN caching - will be replaced by AJAX -->
    <a href="<?php echo Mage::getUrl('checkout/cart') ?>" class="headerCart" id="header-cart-placeholder">
        <span class="number">0</span>
        <?php echo $this->__('products');?>
    </a>

    <script type="text/javascript">
    //<![CDATA[
    // Load dynamic content for header cart section
    if (typeof PFGPunchHole !== 'undefined') {
        // If PunchHole is already loaded, load the section immediately
        PFGPunchHole.loadSection('headerCart');
    } else {
        // Otherwise, wait for it to load
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof PFGPunchHole !== 'undefined') {
                PFGPunchHole.loadSection('headerCart');
            }
        });
    }
    //]]>
    </script>


<?php endif ?>
