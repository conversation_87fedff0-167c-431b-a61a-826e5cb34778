<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php
    $_product = $this->getProduct();
    $_wishlistSubmitUrl = $this->helper('wishlist')->getAddUrl($_product);
    $_compareUrl = $this->helper('catalog/product_compare')->getAddUrl($_product);
    $isLeasingAvailable =
    (Mage::helper('core')->isModuleEnabled('Stenik_LeasingUniCredit') && Mage::helper('stenik_leasingunicredit/product')->isLeasingAvailable($_product, true)) ||
    (Mage::helper('core')->isModuleEnabled('Stenik_LeasingTbi') && Mage::helper('stenik_leasingtbi/product')->isLeasingAvailable($_product, true))
?>

<!-- PFG PUNCH HOLE START: addToCart -->
<div class="addToCart">
    <input type="text" name="qty" id="qty" value="1" title="<?php echo $this->__('Qty') ?>" class="qty amount" readonly />
    <a href="javascript:;" class="addToCartBtn btn-cart" title="<?php echo $this->__('Buy now'); ?>" onclick="productAddToCartForm.submit(this)" style="display:none;">
        <?php echo $this->__('Buy now'); ?>
    </a>
</div>
<a class="buyOnLeasingButton" href="javascript:;" style="display:none;"><?php echo $this->__('Buy on leasing'); ?></a>
<!-- PFG PUNCH HOLE END: addToCart -->
<?php echo $this->getChildHtml('', true, true) ?>


