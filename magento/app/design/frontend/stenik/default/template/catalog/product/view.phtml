<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * Product view template
 *
 * @see Mage_Catalog_Block_Product_View
 * @see Mage_Review_Block_Product_View
 */
?>
<?php $_helper = $this->helper('catalog/output'); ?>
<?php $_baseHelper = $this->helper('stenik_base/catalog_product'); ?>
<?php $_product = $this->getProduct(); ?>
<?php $_wishlistSubmitUrl = $this->helper('wishlist')->getAddUrl($_product); ?>
<?php $_compareUrl = $this->helper('catalog/product_compare')->getAddUrl($_product); ?>
<?php $currentUrl = Mage::helper('core/url')->getCurrentUrl(); ?>
<?php $attributevalue=$_product->getData('shop'); ?>
<?php $currentCategory = Mage::registry('current_category')->getName(); ?>
<?php
    $isLeasingAvailable =
    (Mage::helper('core')->isModuleEnabled('Stenik_LeasingUniCredit') && Mage::helper('stenik_leasingunicredit/product')->isLeasingAvailable($_product, true)) ||
    (Mage::helper('core')->isModuleEnabled('Stenik_LeasingTbi') && Mage::helper('stenik_leasingtbi/product')->isLeasingAvailable($_product, true))
?>

<script>
    var optionsPrice = new Product.OptionsPrice(<?php echo $this->getJsonConfig() ?>);
</script>
<!--
Start of Floodlight Tag on behalf of AdTradr Corporation: Please do not remove
Activity name of this tag: VIP Watches Dynamic All pages
URL of the webpage where the tag is expected to be placed: https://www.vip-watches.net
This tag must be placed between the <body> and </body> tags, as close as possible to the opening tag.
Creation Date: 04/03/2017
-->
<script type="text/javascript">
var axel = Math.random() + "";
var a = axel * 10000000000000;
document.write('<iframe src="https://6739228.fls.doubleclick.net/activityi;src=6739228;type=invmedia;cat=wsx8vxg9;u1=[<?php echo $currentCategory; ?>];u2=[<?php echo $_product->getAttributeText('manufacturer') ?>];u3=[<?php echo $_helper->productAttribute($_product, $_product->getName(), 'name') ?>];u4=[<?php echo Mage::helper('core')->currency(Mage::helper('tax')->getPrice($_product, $_product->getFinalPrice()),false); ?> <?php echo Mage::app()->getStore()->getCurrentCurrencyCode(); ?>];u5=[<?php echo $_product->getId(); ?>];dc_lat=;dc_rdid=;tag_for_child_directed_treatment=;ord=' + a + '?" width="1" height="1" frameborder="0" style="display:none"></iframe>');
</script>
<noscript>
<iframe src="https://6739228.fls.doubleclick.net/activityi;src=6739228;type=invmedia;cat=wsx8vxg9;u1=[<?php echo $currentCategory; ?>];u2=[<?php echo $_product->getAttributeText('manufacturer') ?>];u3=[<?php echo $_helper->productAttribute($_product, $_product->getName(), 'name') ?>];u4=[<?php echo Mage::helper('core')->currency(Mage::helper('tax')->getPrice($_product, $_product->getFinalPrice()),false); ?> <?php echo Mage::app()->getStore()->getCurrentCurrencyCode(); ?>];u5=[<?php echo $_product->getId(); ?>];dc_lat=;dc_rdid=;tag_for_child_directed_treatment=;ord=1?" width="1" height="1" frameborder="0" style="display:none"></iframe>
</noscript>
<script type="text/javascript">
var axel = Math.random() + "";
var a = axel * 10000000000000;
document.write('<iframe src="https://6766500.fls.doubleclick.net/activityi;src=6766500;type=vipwa0;cat=vipwa0;u1=[<?php echo $currentCategory; ?>];u2=[<?php echo $_product->getAttributeText('manufacturer') ?>];u3=[<?php echo $_helper->productAttribute($_product, $_product->getName(), 'name') ?>];u4=[<?php echo Mage::helper('core')->currency(Mage::helper('tax')->getPrice($_product, $_product->getFinalPrice()),false); ?> <?php echo Mage::app()->getStore()->getCurrentCurrencyCode(); ?>];u5=[<?php echo $_product->getId(); ?>];dc_lat=;dc_rdid=;tag_for_child_directed_treatment=;ord=' + a + '?" width="1" height="1" frameborder="0" style="display:none"></iframe>');
</script>
<noscript>
<iframe src="https://6766500.fls.doubleclick.net/activityi;src=6766500;type=vipwa0;cat=vipwa0;u1=[<?php echo $currentCategory; ?>];u2=[<?php echo $_product->getAttributeText('manufacturer') ?>];u3=[<?php echo $_helper->productAttribute($_product, $_product->getName(), 'name') ?>];u4=[<?php echo Mage::helper('core')->currency(Mage::helper('tax')->getPrice($_product, $_product->getFinalPrice()),false); ?> <?php echo Mage::app()->getStore()->getCurrentCurrencyCode(); ?>];u5=[<?php echo $_product->getId(); ?>];dc_lat=;dc_rdid=;tag_for_child_directed_treatment=;ord=1?" width="1" height="1" frameborder="0" style="display:none"></iframe>
</noscript>
<!-- End of Floodlight Tag on behalf of AdTradr Corporation: Please do not remove -->

<div id="messages_product_view"><?php echo $this->getMessagesBlock()->getGroupedHtml() ?></div>

<div class="productViewContent" itemscope itemtype="http://schema.org/Product">

    <div class="productViewLeft">
        <div class="productViewGallery">
            <?php echo $this->getChildHtml('media'); ?>
            <?php echo Mage::app()->getLayout()->getBlock('product.labels')->setProduct($_product)->toHtml(); ?>
            <?php if ($_product->getData('video')): ?>
                <a href="#videoPopUp" class="videoThumbBox">
                    <img src="<?php echo $_baseHelper->generateYoutubeThumbnail($_helper->productAttribute($_product, $_product->getData('video'), 'video')) ?>" alt="video thumb">
                </a>
            <?php endif ?>
            <?php echo Mage::helper('onsale')->getProductLabelHtml($_product); ?>
        </div>
        <?php echo $this->getChildHtml('related_products'); ?>
    </div>


    <div class="productViewRight">
        <h1 itemprop="name"><?php echo $_helper->productAttribute($_product, $_product->getName(), 'name') ?></h1>
        <span class="sku"><?php echo $this->__('Sku number');?>: <strong><?php echo $this->escapeHtml($_product->getSku()) ?></strong></span>
        <div class="productInfo">
            <div class="productInfoLeftCol">
                <div class="addToCartBox">
                    <form action="<?php echo $this->getSubmitUrl($_product) ?>" method="post" id="product_addtocart_form"<?php if($_product->getOptions()): ?> enctype="multipart/form-data"<?php endif; ?> >
                        <?php echo $this->getBlockHtml('formkey') ?>
                        <div class="no-display">
                            <input type="hidden" name="product" value="<?php echo $_product->getId() ?>" />
                            <input type="hidden" name="related_product" id="related-products-field" value="" />
                        </div>
                        <?php echo $this->getChildHtml('alert_urls') ?>
                        <?php echo $this->getChildHtml('extrahint') ?>
                        <?php echo $this->getPriceHtml($_product); ?>
                        <?php if (!$this->hasOptions()):?>
                            <?php if($_product->isSaleable()): ?>
                                <?php echo $this->getChildHtml('addtocart') ?>
                            <?php endif; ?>
                        <?php else: ?>
                            <?php if ($_product->isSaleable() && $this->hasOptions()):?>
                                <?php echo $this->getChildChildHtml('container1', '', true, true) ?>
                                <?php echo $this->getChildChildHtml('container2', '', true, true) ?>
                            <?php endif;?>
                            <?php echo $this->getChildHtml('addtocart') ?>
                        <?php endif; ?>                        
                        <?php echo $this->getChildHtml('extra_buttons') ?>
                        <?php echo $this->getChildHtml('other');?>

                        <?php if (!$_product->getShowRequestForm()): ?>
                            <div style="display:none;">
                                <?php if($_product->getTypeId() == "bundle"): ?>
                                    <div itemprop="offers" itemscope itemtype="http://schema.org/AggregateOffer">
                                        <?php $maxPrice = Mage::helper('tax')->getPrice($_product, Mage::getModel('bundle/product_price')->getTotalPrices($_product,'max',1)); ?>
                                        <?php $minPrice = Mage::helper('tax')->getPrice($_product, Mage::getModel('bundle/product_price')->getTotalPrices($_product,'min',1)); ?>
                                        <meta itemprop="lowPrice" content="<?php echo Mage::helper('core')->currency($minPrice,false); ?>"/>
                                        <meta itemprop="highPrice" content="<?php echo Mage::helper('core')->currency($maxPrice,false); ?>"/>
                                        <meta itemprop="priceCurrency" content="<?php echo Mage::app()->getStore()->getCurrentCurrencyCode(); ?>"/>
                                        <meta itemprop="url" content="<?php echo Mage::helper('core/url')->getCurrentUrl(); ?>"/>
                                        <?php if ($_product->isSaleable()): ?>
                                            <link itemprop="availability" href="http://schema.org/InStock" />
                                        <?php else: ?>
                                            <link itemprop="availability" href="http://schema.org/OutOfStock" />
                                        <?php endif ?>
                                    </div>
                                <?php else: ?>
                                    <div itemprop="offers" itemscope itemtype="http://schema.org/Offer">
                                        <meta itemprop="price" content="<?php echo Mage::helper('core')->currency(Mage::helper('tax')->getPrice($_product, $_product->getFinalPrice()),false); ?>"/>
                                        <meta itemprop="priceCurrency" content="<?php echo Mage::app()->getStore()->getCurrentCurrencyCode(); ?>"/>
                                        <meta itemprop="url" content="<?php echo Mage::helper('core/url')->getCurrentUrl(); ?>"/>
                                        <?php if ($_product->isSaleable()): ?>
                                            <link itemprop="availability" href="http://schema.org/InStock" />
                                        <?php else: ?>
                                            <link itemprop="availability" href="http://schema.org/OutOfStock" />
                                        <?php endif ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </form>                    

                    <a href="javascript:;" class="laser-button"><?php echo $this->__('Engrave laser') ?></a>
                    
                    <?php if ($_product->getPodarukChasovnik()): ?>
                        <?php if ($this->getChildHtml('gift-box-cms')): ?>
                            <div class="giftBoxWrapper">
                                <a href="javascript:;" class="gift-box-button">
                                    <span class="gift-icon"></span>  
                                    <?php echo $this->__('Gift information') ?>
                                </a>
                                <div class="giftBoxPopup">
                                    <?php echo $this->getChildHtml('gift-box-cms'); ?>
                                </div>
                            </div>
                        <?php endif ?>
                    <?php endif ?>

                    <div class="clear"></div>

                    <?php if ($attributevalue): ?>
                        <a href="javascript:;" class="availabilityInStores"><?php echo $this->__('Available in stores');?></a>
                    <?php endif ?>
                </div>
            </div>
            <div class="productInfoRightCol">
                <?php echo $this->getReviewsSummaryHtml($_product, 'default', true)?>
                <div class="line"></div>
                <!-- PFG PUNCH HOLE START: addToCompare -->
                <a href="javascript:;" class="addToCompareist link-compare"><?php echo $this->__('Add to compate list');?></a>
                <!-- PFG PUNCH HOLE END: addToCompare -->
                <div class="line"></div>
                <!-- PFG PUNCH HOLE START: addToWishlist -->
                <a href="javascript:;" class="addToWishlist link-wishlist" onclick="productAddToCartForm.submitLight(this, this.href); return false;" rel="nofollow">
                    <?php echo $this->__('Add to wishlist');?>
                </a>
                <!-- PFG PUNCH HOLE END: addToWishlist -->
                <div class="line"></div>
                <div class="productViewSocial">
                    <span class="socialTitle"><?php echo $this->__('Share');?>:</span>
                    <div class="addThisBox">
                        <a href="javascript:myPopup('https://www.facebook.com/sharer/sharer.php?u=<?php echo $currentUrl; ?>&amp;display=popup');" class="social facebook"></a>
                        <a href="javascript:myPopup('https://plus.google.com/share?url=<?php echo $currentUrl; ?>');" class="social gPlus"></a>
                        <?php
                            $tweeterShareUrl = sprintf("javascript:myPopup('https://twitter.com/intent/tweet?text=%s');",
                                urlencode(sprintf('%s %s %s',
                                    $_helper->productAttribute($_product, $_product->getName(), 'name'),
                                    urlencode('@vip_watches'),
                                    $currentUrl
                                ))
                            );
                        ?>
                        <a href="<?php echo $tweeterShareUrl ?>" class="social twitter"></a>
                    </div>
                </div>
            </div>
            <div class="productInfoAccordion">
                <?php if ($this->getChildHtml('originalProduct')): ?>
                    <div class="accordionItem icon1">
                        <a class="link open" href="javascript:;"><?php echo $this->__('Original product');?></a>
                        <div class="accordionSub">
                            <div class="textPage">
                                <?php echo $this->getChildHtml('originalProduct'); ?>
                            </div>
                        </div>
                    </div>
                <?php endif ?>
                <?php if ($this->getChildHtml('whenWillReceive')): ?>
                    <div class="accordionItem icon2">
                        <a class="link open" href="javascript:;"><?php echo $this->__('When will I receive?');?></a>
                        <div class="accordionSub">
                            <div class="textPage">
                                <?php echo $this->getChildHtml('whenWillReceive'); ?>
                            </div>
                        </div>
                    </div>
                <?php endif ?>
                <div class="accordionItem icon3">
                    <a class="link open" href="javascript:;"><?php echo $this->__('You saw it at a lower price? Write us');?></a>
                    <div class="accordionSub">
                        <div class="textPage">
                            <?php if ($this->getChildHtml('lowerPriceWriteUs')): ?>
                                <?php echo $this->getChildHtml('lowerPriceWriteUs'); ?>
                            <?php endif ?>
                            <?php echo $this->getChildHtml('request_form') ?>
                        </div>
                    </div>
                </div>
                <?php if ($_product->getLaserEngraving() && $this->getChildHtml('handEngraving') && Mage::helper('stenik_personalizedlabel')->isLaserEngravingEnabled()): ?>
                    <div class="accordionItem icon4">
                        <a class="link open" href="javascript:;"><?php echo $this->__('Laser engraving');?></a>
                        <div class="accordionSub">
                            <div class="textPage">
                                <?php echo $this->getChildHtml('handEngraving'); ?>
                            </div>
                            <?php echo $this->getChildHtml('personalized.label.form'); ?>
                        </div>
                    </div>
                <?php endif ?>
            </div>
        </div>
    </div>

    <div class="productViewTabsContnt">
        <div class="productsTabInfo ui-tabs">
            <ul class="ui-tabs-nav">
                <li><a href="#tabs-1"><?php echo $this->__('Description');?></a></li>
                <?php if ($this->getChildHtml('warrantyAndReturn')): ?>
                    <li><a href="#tabs-2"><?php echo $this->__('Warranty and return');?></a></li>
                <?php endif ?>
                <?php if ($this->getChildHtml('shippingAndPayment')): ?>
                    <li><a href="#tabs-3"><?php echo $this->__('Shipping and payment');?></a></li>
                <?php endif ?>
                <?php if ($attributevalue): ?>
                    <li><a class="storeAvailabilityTabLink" href="#tabs-4"><?php echo $this->__('Available in stores');?></a></li>
                <?php endif ?>
                <li><a class="commentsTabLink" href="#tabs-5"><?php echo $this->__('Comments');?></a></li>
                <!-- PFG PUNCH HOLE START: leasing -->
                <li style="display:none;"><a class="leasingTabLink" href="#tabs-6"><?php echo $this->__('Buy on leasing');?></a></li>
                <!-- PFG PUNCH HOLE END: leasing -->
            </ul>
            <div id="tabs-1" class="ui-tabs-panel">
                <div class="textPage">
                    <div class="descriptionContent" itemprop="description">
                        <?php echo $_helper->productAttribute($_product, $_product->getDescription(), 'description') ?>
                    </div>
                    <?php echo $this->getChildHtml('attributes') ?>
                </div>
            </div>
            <?php if ($this->getChildHtml('warrantyAndReturn')): ?>
                <div id="tabs-2" class="ui-tabs-panel">
                    <div class="textPage">
                        <?php echo $this->getChildHtml('warrantyAndReturn'); ?>
                    </div>
                </div>
            <?php endif ?>
            <?php if ($this->getChildHtml('shippingAndPayment')): ?>
                <div id="tabs-3" class="ui-tabs-panel">
                    <div class="textPage">
                        <?php echo $this->getChildHtml('shippingAndPayment'); ?>
                    </div>
                </div>
            <?php endif ?>
            <?php if ($attributevalue): ?>
                <div id="tabs-4" class="ui-tabs-panel">
                    <div class="textPage storeAvailabilityContent">
                    <?php echo $this->getChildHtml('shop.list.product'); ?>
                    </div>
                </div>
            <?php endif; ?>
            <div id="tabs-5" class="ui-tabs-panel">
                <?php echo $this->getChildHtml('reviews_form') ?>
                <?php echo $this->getChildHtml('reviews_list') ?>
            </div>
            <?php if ($isLeasingAvailable): ?>
                <div id="tabs-6" class="leasing-tabs">
                    <div class="tabs-nav-leasing">
                        <ul>
                            <?php if (Mage::helper('core')->isModuleEnabled('Stenik_LeasingUniCredit') && Mage::helper('stenik_leasingunicredit/product')->isLeasingAvailable($_product, true)): ?>
                                <li class="tab-nav-item-wrapper">
                                    <a class="leasing-tabs-nav" href="javascript:;" data-tab-target="#unicredit-leasing">
                                        <img src="<?php echo $this->getSkinUrl('images/unicredit-logo.png') ?>" alt="Unicredit Leasing">
                                    </a>
                                </li>
                            <?php endif ?>
                            <?php if (Mage::helper('core')->isModuleEnabled('Stenik_LeasingTbi') && Mage::helper('stenik_leasingtbi/product')->isLeasingAvailable($_product, true)): ?>
                                <li class="tab-nav-item-wrapper">
                                    <a class="leasing-tabs-nav" href="javascript:;" data-tab-target="#tbi-leasing">
                                        <img src="<?php echo $this->getSkinUrl('images/tbilogo.png') ?>" alt="TBI Leasing">
                                    </a>
                                </li>
                            <?php endif ?>
                        </ul>
                    </div>
                    <div class="tabs-content">
                        <?php if (Mage::helper('core')->isModuleEnabled('Stenik_LeasingTbi') && Mage::helper('stenik_leasingtbi/product')->isLeasingAvailable($_product, true)): ?>
                            <div class="leasing-tab" id="tbi-leasing">
                                <?php echo $this->getChildHtml('tbi.calculator'); ?>
                                <a href="javascript:;" class="button checkout-color leasing-add-to-cart"><?php echo $this->__('Buy on leasing') ?></a>
                                <p class="leasing-message">* <?php echo $this->__('The number of repayment installments is selected on completion of the order') ?></p>
                            </div>
                        <?php endif ?>
                        <?php if (Mage::helper('core')->isModuleEnabled('Stenik_LeasingUniCredit') && Mage::helper('stenik_leasingunicredit/product')->isLeasingAvailable($_product, true)): ?>
                            <div class="leasing-tab" id="unicredit-leasing">
                                <?php echo $this->getChildHtml('unicredit.calculator'); ?>
                                <a href="javascript:;" class="button checkout-color leasing-add-to-cart"><?php echo $this->__('Buy on leasing') ?></a>
                                <p class="leasing-message">* <?php echo $this->__('The number of repayment installments is selected on completion of the order') ?></p>
                            </div>
                        <?php endif ?>
                    </div>
                </div>
            <?php endif ?>
        </div>
    </div>

    <?php echo $this->getChildHtml('upsell_products') ?>

    <?php echo $this->getLayout()->createBlock('reports/product_viewed')->setTemplate('reports/product_viewed.phtml')->toHtml(); ?>
</div>


<div style="display:none;">
    <div class="videoPopUpContent" id="videoPopUp">
        <?php echo $_baseHelper->generateYoutubeEmbeds($_helper->productAttribute($_product, $_product->getData('video'), 'video'), true, 800, 600) ?>
    </div>
</div>

<script>
    function resizeColorboxOnVarienFormValidation(varienForm) {
        if (varienForm.validator) {
            var oldValidate = varienForm.validator.validate;
            varienForm.validator.validate = function(){
                var result = oldValidate.call(this);
                var resizeColorboxTimeoutId = false;
                var requestColorboxResize = function() {
                    if (resizeColorboxTimeoutId) {
                        clearTimeout(resizeColorboxTimeoutId);
                        resizeColorboxTimeoutId = false;
                    }
                    resizeColorboxTimeoutId = setTimeout(jQuery.colorbox.resize.bind(jQuery.colorbox), 20);
                }
                if (this.form) {
                    this.form.select('.validation-advice').each(function(adviceElm){
                        if (!adviceElm.colorboxResizeInited) {
                            oldShow = adviceElm.show;
                            oldHide = adviceElm.hide;
                            oldForceRendering = adviceElm.forceRerendering;
                            adviceElm.show = function() {
                                oldShow.call(adviceElm);
                                requestColorboxResize();
                            }
                            adviceElm.hide = function() {
                                oldHide.call(adviceElm);
                                requestColorboxResize();
                            }
                            adviceElm.forceRerendering = function() {
                                requestColorboxResize();
                                oldForceRendering.call(adviceElm);
                            }
                            adviceElm.colorboxResizeInited = true;
                        }
                    });
                }

                return result;
            }
        }
    }

    var productAddToCartForm   = new VarienForm('product_addtocart_form');
    var productAddToQuickOrder = new VarienForm('quickorder_dummy_form');

    resizeColorboxOnVarienFormValidation(productAddToQuickOrder);

    jQuery(function($){

        $(".productsTabInfo").tabs({
           activate: function() {
               $('.leasing-tabs').each(function() {
                   var leasingTabs = new Stenik.Tabs({
                       $tabsWrapper: $(this),
                       $tabContents: $(this).find('.leasing-tab'),
                       $tabNavItems: $(this).find('.leasing-tabs-nav'),
                       navItemInitial: 0,
                       onTabActivateAfter: function() {
                           $('.jetCredit-tabs').each(function() {
                               var leasingTabs2 = new Stenik.Tabs({
                                   $tabsWrapper: $(this),
                                   $tabContents: $(this).find('.tab'),
                                   $tabNavItems: $(this).find('.tab-nav-item'),
                                   navItemInitial: 0
                               });
                           });
                       }
                   });
               });
           }
        });

        $(".infoRatingBox").click(function() {
            $('html, body').animate({
                scrollTop: $(".productViewTabsContnt").offset().top - 40
            }, 500);
            setTimeout(function(){
                $(".productsTabInfo  a.commentsTabLink").click();
            }, 530);
        });

        $(".availabilityInStores").click(function() {
            $('html, body').animate({
                scrollTop: $(".productViewTabsContnt").offset().top
            }, 400);
            setTimeout(function(){
                $(".productsTabInfo  a.storeAvailabilityTabLink").click();
                $('.shopsListing .shopsBox').first().find('.viewOnMap').click();
            }, 430);
            setTimeout(function(){
                $('.shopsListing .shopsBox').first().find('.viewOnMap').click();
            }, 440);
        });

        $(".storeAvailabilityTabLink").click(function() {
            setTimeout(function(){
                $('.shopsListing .shopsBox').first().find('.viewOnMap').click();
            }, 100);
        });

        $(".laser-button").click(function() {
            $('html, body').animate({
                scrollTop: $(".productInfoAccordion").offset().top
            }, 400);
            setTimeout(function(){
                $(".productInfoAccordion  .icon4 .link").click();
            }, 430);
        });

        $(".buyOnLeasingButton").click(function() {
            $('html, body').animate({
                scrollTop: $(".productViewTabsContnt").offset().top - 40
            }, 400);
            setTimeout(function(){
                $(".productsTabInfo  a.leasingTabLink").click();
            }, 430);
        });

        <?php if ($_product->isSaleable()):?>
            $('.leasing-add-to-cart').click(function() {
                $('html,body').animate({
                    scrollTop: $('.productViewContent').offset().top -80
                }, 'slow');
                $('.productViewContent .addToCartBtn').click();
            });
        <?php else: ?>
            $('.leasing-add-to-cart').click(function() {
                $('.productViewContent .addToCartBtn').click();
            });
        <?php endif;?>

        $(".productListingSlider").owlCarousel({
            loop: true,
            items: 4,
            navigation: true,
            navigationText: false,
            slideSpeed: 500,
            autoPlay: false,
            autoplayTimeout: 5000,
            autoplayHoverPause: true,
            addClassActive: true,
            dots: true,
            itemsDesktop: false,
            itemsDesktopSmall: false,
            itemsTablet: [1023, 4],
            itemsTabletSmall: false,
            itemsMobile: [599, 2]
        });

        $('.videoThumbBox').colorbox({inline:true});

    });

    productAddToCartForm.submit = function(button, url) {
        if (this.validator.validate()) {
            var form = this.form;
            var oldUrl = form.action;

            if (url) {
               form.action = url;
            }
            var e = null;
            try {
                this.form.submit();
            } catch (e) {
            }
            this.form.action = oldUrl;
            if (e) {
                throw e;
            }

            if (button && button != 'undefined') {
                button.disabled = true;
            }
        }
    }.bind(productAddToCartForm);

    productAddToCartForm.submitLight = function(button, url){
        if(this.validator) {
            var nv = Validation.methods;
            delete Validation.methods['required-entry'];
            delete Validation.methods['validate-one-required'];
            delete Validation.methods['validate-one-required-by-name'];
            // Remove custom datetime validators
            for (var methodName in Validation.methods) {
                if (methodName.match(/^validate-datetime-.*/i)) {
                    delete Validation.methods[methodName];
                }
            }

            if (this.validator.validate()) {
                if (url) {
                    this.form.action = url;
                }
                this.form.submit();
            }
            Object.extend(Validation.methods, nv);
        }
    }.bind(productAddToCartForm);

    function myPopup(url) {
        window.open( url, "myWindow", "status = 1, height = 500, width = 525, resizable = 0" );
    }
</script>
