<?php
    $_helper        = Mage::helper('stenik_personalizedlabel');
    $_product       = $this->getProduct();
    $buttonTitle    = $this->__('Add');
?>

<!-- PFG PUNCH HOLE START: personalizedLabel -->
<div class="section personalized-label-section" style="display:none;">
    <form action="<?php echo $this->getSubmitUrl($_product, array('_secure' => $this->_isSecure())) ?>" method="post" id="product_addtocart_form_personalizedlabel">
        <?php echo $this->getBlockHtml('formkey') ?>
        <input type="hidden" name="product" value="<?php echo $_product->getId(); ?>" />
        <input type="hidden" name="related_product" id="related-products-field" value="" />
        <input type="hidden" name="personalized_label" value="1" />
        <input type="hidden" id="stenik_personalizedlabel_font_value" name="stenik_personalizedlabel_font" value="" />
        <div class="personalized-label-form">
            <div class="input-box">
                <input type="text" class="input-text required-entry" placeholder="<?php echo $_helper->__('Wish:'); ?> *" id="stenik_personalizedlabel_wish" name="stenik_personalizedlabel_wish" />
            </div>
            <div class="input-box">
                <select class="input-text required-entry" id="stenik_personalizedlabel_font" name="stenik_personalizedlabel_font_select">
                    <option value=""><?php echo $_helper->__('Font Selection'); ?> *</option>
                    <option data-imagesrc="<?php echo $this->getSkinUrl('images/font1.png') ?>" class="font1" value="Font 1"></option>
                    <option data-imagesrc="<?php echo $this->getSkinUrl('images/font2.png') ?>" class="font2" value="Font 2"></option>
                    <option data-imagesrc="<?php echo $this->getSkinUrl('images/font3.png') ?>" class="font3" value="Font 3"></option>
                    <option data-imagesrc="<?php echo $this->getSkinUrl('images/font4.png') ?>" class="font4" value="Font 4"></option>
                    <option data-imagesrc="<?php echo $this->getSkinUrl('images/font5.png') ?>" class="font5" value="Font 5"></option>
                    <option data-imagesrc="<?php echo $this->getSkinUrl('images/font6.png') ?>" class="font6" value="Font 6"></option>
                    <option data-imagesrc="<?php echo $this->getSkinUrl('images/font7.png') ?>" class="font7" value="Font 7"></option>
                    <option data-imagesrc="<?php echo $this->getSkinUrl('images/font8.png') ?>" class="font8" value="Font 8"></option>
                </select>
            </div>
            <button type="button" title="<?php echo $buttonTitle ?>" id="product-addtocart-button-personalizedlabel" class="button btn-cart" onclick="productAddToCartFormPersonalizedLabel.submit(this)"><?php echo $buttonTitle ?></button>
        </div>
    </form>
    <script type="text/javascript">
        jQuery(function($) {
            $('#stenik_personalizedlabel_font').ddslick({
                onSelected: function (data) {
                    $('#stenik_personalizedlabel_font_value').val(data.selectedData.value);
                }
            });

            $('.dd-selected-value').addClass('required-entry').val('');
        });
        //<![CDATA[
            var productAddToCartFormPersonalizedLabel = new VarienForm('product_addtocart_form_personalizedlabel');
            productAddToCartFormPersonalizedLabel.submit = function(button, url) {
                if (this.validator.validate()) {
                    var form = this.form;
                    var oldUrl = form.action;

                    if (url) {
                       form.action = url;
                    }
                    var e = null;
                    try {
                        this.form.submit();
                    } catch (e) {
                    }
                    this.form.action = oldUrl;
                    if (e) {
                        throw e;
                    }

                    if (button && button != 'undefined') {
                        button.disabled = true;
                    }
                }
            }.bind(productAddToCartFormPersonalizedLabel);
        //]]>
    </script>
</div>
<!-- PFG PUNCH HOLE END: personalizedLabel -->
