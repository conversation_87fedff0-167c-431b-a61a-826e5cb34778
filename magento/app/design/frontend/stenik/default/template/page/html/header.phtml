<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/**
 * @var Mage_Page_Block_Html_Header $this
 */
?>

<header>
    <!--wb7-->
    <div class="headerTop">
        <div class="wrapper">
            <?php if ($this->getChildHtml('headerPhone')): ?>
                <div class="headerPhone">
                    <?php echo $this->getChildHtml('headerPhone'); ?>
                </div>
            <?php endif ?>
            <?php if ($this->getChildHtml('headerDelivery')): ?>
                <div class="headerTopDelivery">
                    <?php echo $this->getChildHtml('headerDelivery'); ?>
                </div>
            <?php endif ?>
            <?php echo $this->getChildHtml('headerCustomer'); ?>
        </div>
    </div>
    <div class="headerContent">
        <div class="wrapper">
            <div class="logoWrapper" itemscope itemtype="http://schema.org/Organization">
                <a href="<?php echo $this->getUrl('') ?>" class="logo" itemprop="url" rel="home"><img itemprop="logo" src="<?php echo $this->getLogoSrc() ?>" alt="<?php echo $this->getLogoAlt() ?>"></a>
            </div>
            <?php echo $this->getChildHtml('topSearch') ?>
            <?php echo $this->getChildHtml('headerCart'); ?>
            <?php echo $this->getChildHtml('topMenu') ?>
        </div>
    </div>
</header>


<div class="responsiveHeader">
    <div class="responsiveMenu">
        <a href="javascript:;" class="openResponsiveMenu">
            <span><?php echo $this->__('menu');?></span>
        </a>
        <div class="responsiveMenuSub">
            <?php echo $this->getChildHtml('topSearchResponsive'); ?>
            <?php echo $this->getChildHtml('topMenuResponsive') ?>
            <?php echo $this->getChildHtml('headerResponsiveCustomer'); ?>
        </div>
    </div>
    <a href="<?php echo $this->getUrl('') ?>" class="responsiveLogo"><img src="<?php echo $this->getSkinUrl('images/responsiveLogo.png') ?>" alt="<?php echo $this->getLogoAlt() ?>"></a>
    <a href="tel:<?php echo Mage::getStoreConfig('general/store_information/phone'); ?>" class="responsiveCall"></a>
    <!-- Responsive Cart - Punch Hole Placeholder -->
    <a href="<?php echo $this->getUrl('checkout/cart') ?>" class="responsiveCart" id="responsive-cart-placeholder">
        <span class="responsiveCartitems">0</span>
    </a>

    <script type="text/javascript">
    //<![CDATA[
    // Load dynamic content for responsive cart section
    if (typeof PFGPunchHole !== 'undefined') {
        // If PunchHole is already loaded, load the section immediately
        PFGPunchHole.loadSection('responsiveCart');
    } else {
        // Otherwise, wait for it to load
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof PFGPunchHole !== 'undefined') {
                PFGPunchHole.loadSection('responsiveCart');
            }
        });
    }
    //]]>
    </script>
</div>

