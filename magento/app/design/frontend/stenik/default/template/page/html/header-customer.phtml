
<?php
/**
 * Header Customer Section - Punch Hole Caching
 * This template now renders a placeholder that will be populated via AJAX
 * to enable full page caching while maintaining dynamic user-specific content
 */
?>

<div class="headerRight" id="header-customer-placeholder">
    <!-- Placeholder content for CDN caching - will be replaced by AJAX -->
    <a href="javascript:;" class="compare" title="<?php echo $this->__('Compare');?>">
        <?php echo $this->__('Compare');?>
        <span class="number" style="display:none;">0</span>
    </a>
    <a href="<?php echo $this->getUrl('wishlist') ?>" class="wishlist" title="<?php echo $this->__('Wishlist ');?>">
        <?php echo $this->__('Wishlist ');?>
        <span class="number" style="display:none;">0</span>
    </a>
    <a href="<?php echo $this->getUrl('customer/account/login') ?>" class="login" title="<?php echo $this->__('Login');?>">
        <?php echo $this->__('Login');?>
    </a>
</div>

<!-- Load PFG Punch Hole JavaScript if not already loaded -->
<script type="text/javascript">
if (typeof PFGPunchHole === 'undefined') {
    var script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = '<?php echo $this->getSkinUrl('js/pfg-punchhole.js'); ?>';
    script.onload = function() {
        if (typeof PFGPunchHole !== 'undefined') {
            PFGPunchHole.init('<?php echo Mage::getBaseUrl(); ?>', false);
            PFGPunchHole.loadAllSections(); // OPTIMIZED: Single AJAX call for all sections
        }
    };
    document.head.appendChild(script);
} else {
    // PFGPunchHole is already loaded, but only load all sections once
    if (!PFGPunchHole.sectionsLoaded) {
        PFGPunchHole.loadAllSections();
        PFGPunchHole.sectionsLoaded = true;
    }
}
</script>