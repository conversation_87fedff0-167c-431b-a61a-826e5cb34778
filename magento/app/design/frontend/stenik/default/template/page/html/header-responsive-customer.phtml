
<?php
/**
 * Responsive Header Customer Section - Punch Hole Caching
 * This template now renders a placeholder that will be populated via AJAX
 * to enable full page caching while maintaining dynamic user-specific content
 */
?>

<div id="responsive-customer-placeholder">
    <!-- Placeholder content for CDN caching - will be replaced by AJAX -->
    <span class="accountLinks">
        <a href="<?php echo $this->getUrl('customer/account/login') ?>"><?php echo $this->__('Login');?></a>
        <span class="sep">/</span>
        <a href="<?php echo $this->getUrl('customer/account/create') ?>"><?php echo $this->__('Registration');?></a>
    </span>
    <a href="<?php echo $this->getUrl('wishlist') ?>" class="wishtlistItems">
        <?php echo $this->__('My Wishlist');?>
        <span class="responsiveWishlistitems"> - 0 <?php echo $this->__('added');?></span>
    </a>
    <a href="javascript:;" class="responsiveCompare" title="<?php echo $this->__('Compare');?>">
        <?php echo $this->__('Compare');?>
        <span class="number" style="display:none;">0</span>
    </a>
</div>

<script type="text/javascript">
//<![CDATA[
// Load dynamic content for responsive customer section
if (typeof PFGPunchHole !== 'undefined') {
    // If PunchHole is already loaded, load the section immediately
    PFGPunchHole.loadSection('responsiveCustomer');
} else {
    // Otherwise, wait for it to load
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof PFGPunchHole !== 'undefined') {
            PFGPunchHole.loadSection('responsiveCustomer');
        }
    });
}
//]]>
</script>