<?xml version="1.0"?>
<!--
/**
 * PFG Punch Hole Layout
 * Handles JavaScript initialization for different page types
 * 
 * @category   PFG
 * @package    PFG_PunchHole
 * <AUTHOR> Development Team
 */
-->
<layout version="0.1.0">
    <!-- Default layout - initialize punch hole system -->
    <default>
        <reference name="before_body_end">
            <block type="core/text" name="pfg_punchhole_init">
                <action method="setText">
                    <text><![CDATA[
<script type="text/javascript">
// Initialize PFG Punch Hole system
if (typeof PFGPunchHole !== 'undefined') {
    PFGPunchHole.init('{{base_url}}', false);
}
</script>
                    ]]></text>
                </action>
            </block>
        </reference>
    </default>

    <!-- Category pages - load category sections -->
    <catalog_category_default>
        <reference name="before_body_end">
            <block type="core/text" name="pfg_punchhole_category_init">
                <action method="setText">
                    <text><![CDATA[
<script type="text/javascript">
// Category page - load all sections
if (typeof PFGPunchHole !== 'undefined') {
    PFGPunchHole.loadAllSections();
}
</script>
                    ]]></text>
                </action>
            </block>
        </reference>
    </catalog_category_default>

    <!-- Product view pages - load product-specific sections -->
    <catalog_product_view>
        <reference name="before_body_end">
            <block type="core/text" name="pfg_punchhole_product_init">
                <action method="setText">
                    <text><![CDATA[
<script type="text/javascript">
// Product page - punch hole initialization is handled automatically by the main JS file
console.log('PFGPunchHole: Product page detected, initialization will be handled by DOM ready event');
</script>
                    ]]></text>
                </action>
            </block>
        </reference>
    </catalog_product_view>

    <!-- Cart page - refresh customer and cart sections -->
    <checkout_cart_index>
        <reference name="before_body_end">
            <block type="core/text" name="pfg_punchhole_cart_init">
                <action method="setText">
                    <text><![CDATA[
<script type="text/javascript">
// Cart page - refresh customer and cart sections
if (typeof PFGPunchHole !== 'undefined') {
    setTimeout(function() {
        PFGPunchHole.refreshSection('headerCustomer');
        PFGPunchHole.refreshSection('headerCart');
        PFGPunchHole.refreshSection('responsiveCustomer');
        PFGPunchHole.refreshSection('responsiveCart');
    }, 500);
}
</script>
                    ]]></text>
                </action>
            </block>
        </reference>
    </checkout_cart_index>

    <!-- Login page - refresh customer sections after login -->
    <customer_account_loginPost>
        <reference name="before_body_end">
            <block type="core/text" name="pfg_punchhole_login_init">
                <action method="setText">
                    <text><![CDATA[
<script type="text/javascript">
// Login page - refresh customer sections
if (typeof PFGPunchHole !== 'undefined') {
    setTimeout(function() {
        PFGPunchHole.refreshSection('headerCustomer');
        PFGPunchHole.refreshSection('responsiveCustomer');
    }, 500);
}
</script>
                    ]]></text>
                </action>
            </block>
        </reference>
    </customer_account_loginPost>

    <!-- Logout page - refresh customer sections after logout -->
    <customer_account_logoutSuccess>
        <reference name="before_body_end">
            <block type="core/text" name="pfg_punchhole_logout_init">
                <action method="setText">
                    <text><![CDATA[
<script type="text/javascript">
// Logout page - refresh customer sections
if (typeof PFGPunchHole !== 'undefined') {
    setTimeout(function() {
        PFGPunchHole.refreshSection('headerCustomer');
        PFGPunchHole.refreshSection('responsiveCustomer');
    }, 500);
}
</script>
                    ]]></text>
                </action>
            </block>
        </reference>
    </customer_account_logoutSuccess>
</layout>
