<?xml version="1.0"?>
<!--
/**
 * PFG Punch Hole Layout Configuration
 * 
 * @category   PFG
 * @package    PFG_PunchHole
 */
-->
<layout version="0.1.0">
    
    <!-- Add punch hole JavaScript to all frontend pages -->
    <default>
        <reference name="head">
            <!-- Include the punch hole JavaScript directly -->
            <block type="core/text" name="punchhole.js">
                <action method="setText">
                    <text><![CDATA[
<script type="text/javascript" src="<?php echo $this->getSkinUrl('js/pfg-punchhole.js'); ?>"></script>
                    ]]></text>
                </action>
            </block>

            <!-- Initialize the punch hole system -->
            <block type="core/text" name="punchhole.init">
                <action method="setText">
                    <text><![CDATA[
<script type="text/javascript">
//<![CDATA[
// Initialize PFG Punch Hole system when page loads
document.addEventListener('DOMContentLoaded', function() {
    if (typeof PFGPunchHole !== 'undefined') {
        PFGPunchHole.init('<?php echo Mage::getBaseUrl(); ?>', false);
    }
});
//]]>
</script>
                    ]]></text>
                </action>
            </block>
        </reference>
    </default>
    
    <!-- Specific layout for pages that need punch hole placeholders -->
    <catalog_category_default>
        <reference name="head">
            <block type="core/text" name="punchhole.category.init">
                <action method="setText">
                    <text><![CDATA[
<script type="text/javascript">
//<![CDATA[
// Category-specific initialization if needed
//]]>
</script>
                    ]]></text>
                </action>
            </block>
        </reference>
    </catalog_category_default>
    
    <catalog_product_view>
        <reference name="head">
            <block type="core/text" name="punchhole.product.init">
                <action method="setText">
                    <text><![CDATA[
<script type="text/javascript">
//<![CDATA[
// Product page specific initialization
// Refresh sections after add to cart
if (typeof jQuery !== 'undefined') {
    jQuery(document).ready(function() {
        // Listen for add to cart button clicks
        jQuery('.btn-cart, .add-to-cart').on('click', function() {
            setTimeout(function() {
                if (typeof PFGPunchHole !== 'undefined') {
                    PFGPunchHole.refreshSection('headerCart');
                    PFGPunchHole.refreshSection('responsiveCart');
                }
            }, 1000);
        });
        
        // Listen for add to wishlist
        jQuery('.link-wishlist').on('click', function() {
            setTimeout(function() {
                if (typeof PFGPunchHole !== 'undefined') {
                    PFGPunchHole.refreshSection('headerCustomer');
                    PFGPunchHole.refreshSection('responsiveCustomer');
                }
            }, 1000);
        });
        
        // Listen for add to compare
        jQuery('.link-compare').on('click', function() {
            setTimeout(function() {
                if (typeof PFGPunchHole !== 'undefined') {
                    PFGPunchHole.refreshSection('headerCustomer');
                    PFGPunchHole.refreshSection('responsiveCustomer');
                }
            }, 1000);
        });
    });
}
//]]>
</script>
                    ]]></text>
                </action>
            </block>
        </reference>
    </catalog_product_view>
    
    <checkout_cart_index>
        <reference name="head">
            <block type="core/text" name="punchhole.cart.init">
                <action method="setText">
                    <text><![CDATA[
<script type="text/javascript">
//<![CDATA[
// Cart page specific initialization
if (typeof jQuery !== 'undefined') {
    jQuery(document).ready(function() {
        // Refresh cart sections when cart is updated
        jQuery(document).on('ajaxComplete', function(event, xhr, settings) {
            if (settings.url && settings.url.indexOf('checkout') !== -1) {
                setTimeout(function() {
                    if (typeof PFGPunchHole !== 'undefined') {
                        PFGPunchHole.refreshSection('headerCart');
                        PFGPunchHole.refreshSection('responsiveCart');
                    }
                }, 500);
            }
        });
    });
}
//]]>
</script>
                    ]]></text>
                </action>
            </block>
        </reference>
    </checkout_cart_index>
    
    <customer_account_login>
        <reference name="head">
            <block type="core/text" name="punchhole.login.init">
                <action method="setText">
                    <text><![CDATA[
<script type="text/javascript">
//<![CDATA[
// Login page - refresh customer sections after successful login
if (typeof jQuery !== 'undefined') {
    jQuery(document).ready(function() {
        // Monitor for successful login (redirect or page change)
        var originalUrl = window.location.href;
        
        setInterval(function() {
            if (window.location.href !== originalUrl) {
                if (typeof PFGPunchHole !== 'undefined') {
                    PFGPunchHole.refreshSection('headerCustomer');
                    PFGPunchHole.refreshSection('responsiveCustomer');
                }
            }
        }, 1000);
    });
}
//]]>
</script>
                    ]]></text>
                </action>
            </block>
        </reference>
    </customer_account_login>
    
    <customer_account_logout>
        <reference name="head">
            <block type="core/text" name="punchhole.logout.init">
                <action method="setText">
                    <text><![CDATA[
<script type="text/javascript">
//<![CDATA[
// Logout page - refresh customer sections
if (typeof PFGPunchHole !== 'undefined') {
    setTimeout(function() {
        PFGPunchHole.refreshSection('headerCustomer');
        PFGPunchHole.refreshSection('responsiveCustomer');
    }, 500);
}
//]]>
</script>
                    ]]></text>
                </action>
            </block>
        </reference>
    </customer_account_logout>
    
</layout>
