<?php
/**
 * PFG Analytics Template
 * 
 * This template displays the analytics dashboard with native Magento styling
 * 
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */

// Get sales data for display
$salesData = $this->getSalesData();
$chartData = $this->getChartData();
$statusMappings = $this->getStatusMappings();
$selectedStatuses = $this->getSelectedStatuses();
$comparisonMode = $this->getComparisonMode();
$chartGrouping = $this->getChartGrouping();

// Include external CSS and JavaScript files
$head = $this->getLayout()->getBlock('head');
if ($head) {
    $head->addCss('css/pfg/analytics.css');
    $head->addJs('pfg/analytics.js');
}
?>

<div class="content-header">
    <table cellspacing="0">
        <tr>
            <td><h3 class="icon-head head-report"><?php echo $this->__('PFG Analytics') ?></h3></td>
        </tr>
    </table>
</div>

<!-- Filter Form -->
<div class="entry-edit">
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Date Range') ?></h4>
    </div>
    <div class="fieldset">
        <form id="filter_form" action="<?php echo $this->getFilterUrl() ?>" method="post">
            <input type="hidden" name="form_key" value="<?php echo Mage::getSingleton('core/session')->getFormKey() ?>" />
            
            <table cellspacing="0" class="form-list">
                <tbody>
                    <!-- Date Range -->
                    <tr>
                        <td class="label"><label for="from_date"><?php echo $this->__('From:') ?></label></td>
                        <td class="value">
                            <input type="text" id="from_date" name="from_date" value="<?php echo $this->getFromDate() ?>" class="input-text" readonly="readonly" />
                            <img src="<?php echo $this->getSkinUrl('images/grid-cal.gif') ?>" id="from_date_trig" />
                        </td>
                        <td class="label"><label for="to_date"><?php echo $this->__('To:') ?></label></td>
                        <td class="value">
                            <input type="text" id="to_date" name="to_date" value="<?php echo $this->getToDate() ?>" class="input-text" readonly="readonly" />
                            <img src="<?php echo $this->getSkinUrl('images/grid-cal.gif') ?>" id="to_date_trig" />
                        </td>
                    </tr>
                    
                    <!-- Comparison Mode -->
                    <tr>
                        <td class="label"><label for="comparison_mode"><?php echo $this->__('Comparison:') ?></label></td>
                        <td class="value">
                            <select id="comparison_mode" name="comparison_mode" class="select" onchange="toggleComparisonDates()">
                                <?php 
                                $currentComparison = $this->getComparisonMode();
                                foreach ($this->getComparisonModes() as $mode => $label): 
                                ?>
                                    <option value="<?php echo $mode ?>" <?php echo ($currentComparison === $mode) ? 'selected="selected"' : '' ?>>
                                        <?php echo $label ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                        <td colspan="2"></td>
                    </tr>



                    <!-- Order Statuses -->
                    <tr>
                        <td class="label"><label for="order_statuses"><?php echo $this->__('Order Statuses:') ?></label></td>
                        <td class="value" colspan="3">
                            <select id="order_statuses" name="order_statuses[]" multiple="multiple" class="multiselect">
                                <?php
                                $selectedStatuses = $this->getSelectedStatuses();
                                $allStatuses = $this->getOrderStatuses();
                                ?>
                                <option value="" <?php echo empty($selectedStatuses) ? 'selected="selected"' : '' ?>>
                                    <?php echo $this->__('All Statuses') ?>
                                </option>
                                <?php foreach ($allStatuses as $statusCode => $statusLabel): ?>
                                    <option value="<?php echo $statusCode ?>"
                                            <?php echo in_array($statusCode, $selectedStatuses) ? 'selected="selected"' : '' ?>>
                                        <?php echo $statusLabel ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                    </tr>
                    
                    <!-- Quick Filters -->
                    <tr>
                        <td class="label"><?php echo $this->__('Quick Filters:') ?></td>
                        <td class="value" colspan="3">
                            <button type="button" class="scalable" onclick="applyQuickFilter('30days')"><?php echo $this->__('Last 30 Days') ?></button>
                            <button type="button" class="scalable" onclick="applyQuickFilter('90days')"><?php echo $this->__('Last 90 Days') ?></button>
                            <button type="button" class="scalable" onclick="applyQuickFilter('ytd')"><?php echo $this->__('Year to Date') ?></button>
                            <button type="button" class="scalable" onclick="applyQuickFilter('q1')"><?php echo $this->__('Q1 (Jan-Mar)') ?></button>
                            <button type="button" class="scalable" onclick="applyQuickFilter('q2')"><?php echo $this->__('Q2 (Apr-Jun)') ?></button>
                            <button type="button" class="scalable" onclick="applyQuickFilter('q3')"><?php echo $this->__('Q3 (Jul-Sep)') ?></button>
                            <button type="button" class="scalable" onclick="applyQuickFilter('q4')"><?php echo $this->__('Q4 (Oct-Dec)') ?></button>
                        </td>
                    </tr>
                    
                    <!-- Submit Button -->
                    <tr>
                        <td class="label"></td>
                        <td class="value" colspan="3">
                            <button type="submit" class="scalable save"><?php echo $this->__('Apply Filters') ?></button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
    </div>
</div>



<!-- Sales Statistics -->
<div class="entry-edit">
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Sales Statistics') ?></h4>
    </div>
    <div class="fieldset sales-stats-section">
        <?php if ($salesData['total_orders'] > 0): ?>
            <div class="grid">
                <table cellspacing="0" class="data">
                    <thead>
                        <tr class="headings">
                            <th><?php echo $this->__('Metric') ?></th>
                            <th><?php echo $this->__('Current Period') ?></th>
                            <?php if (isset($salesData['comparison'])): ?>
                                <th><?php echo $this->__('Previous Period') ?></th>
                                <th><?php echo $this->__('Change') ?></th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><?php echo $this->__('Total Orders') ?></td>
                            <td><?php echo number_format($salesData['total_orders']) ?></td>
                            <?php if (isset($salesData['comparison'])): ?>
                                <td><?php echo number_format($salesData['comparison']['total_orders']) ?></td>
                                <td>
                                    <?php
                                    $change = $salesData['total_orders_change'];
                                    $class = $change['direction'] === 'up' ? 'notice' : ($change['direction'] === 'down' ? 'error' : '');
                                    $icon = $change['direction'] === 'up' ? '↑' : ($change['direction'] === 'down' ? '↓' : '→');
                                    ?>
                                    <span class="<?php echo $class ?>">
                                        <?php echo $icon ?> <?php echo $change['percentage'] ?>%
                                        <?php if ($change['is_new']): ?>
                                            <em><?php echo $this->__('New!') ?></em>
                                        <?php endif; ?>
                                    </span>
                                </td>
                            <?php endif; ?>
                        </tr>
                        <tr>
                            <td><?php echo $this->__('Total Amount') ?></td>
                            <td><?php echo $this->formatCurrency($salesData['total_amount']) ?></td>
                            <?php if (isset($salesData['comparison'])): ?>
                                <td><?php echo $this->formatCurrency($salesData['comparison']['total_amount']) ?></td>
                                <td>
                                    <?php
                                    $change = $salesData['total_amount_change'];
                                    $class = $change['direction'] === 'up' ? 'notice' : ($change['direction'] === 'down' ? 'error' : '');
                                    $icon = $change['direction'] === 'up' ? '↑' : ($change['direction'] === 'down' ? '↓' : '→');
                                    ?>
                                    <span class="<?php echo $class ?>">
                                        <?php echo $icon ?> <?php echo $change['percentage'] ?>%
                                        <?php if ($change['is_new']): ?>
                                            <em><?php echo $this->__('New!') ?></em>
                                        <?php endif; ?>
                                    </span>
                                </td>
                            <?php endif; ?>
                        </tr>
                        <tr>
                            <td><?php echo $this->__('Average Order Amount') ?></td>
                            <td><?php echo $this->formatCurrency($salesData['average_amount']) ?></td>
                            <?php if (isset($salesData['comparison'])): ?>
                                <td><?php echo $this->formatCurrency($salesData['comparison']['average_amount']) ?></td>
                                <td>
                                    <?php
                                    $change = $salesData['average_amount_change'];
                                    $class = $change['direction'] === 'up' ? 'notice' : ($change['direction'] === 'down' ? 'error' : '');
                                    $icon = $change['direction'] === 'up' ? '↑' : ($change['direction'] === 'down' ? '↓' : '→');
                                    ?>
                                    <span class="<?php echo $class ?>">
                                        <?php echo $icon ?> <?php echo $change['percentage'] ?>%
                                        <?php if ($change['is_new']): ?>
                                            <em><?php echo $this->__('New!') ?></em>
                                        <?php endif; ?>
                                    </span>
                                </td>
                            <?php endif; ?>
                        </tr>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p class="note">
                <span><?php echo $this->__('No orders found for the selected date range.') ?></span>
            </p>
        <?php endif; ?>
    </div>
</div>

<!-- Daily Purchases Chart -->
<?php if ($this->isValidDateRange()): ?>
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Daily Purchases Chart') ?></h4>
        </div>
        <div class="fieldset">
            <!-- Chart Grouping Options -->
            <div class="chart-controls" style="margin-bottom: 15px; padding: 10px; background: #f8f8f8; border: 1px solid #ddd;">
                <label for="chart_grouping" style="font-weight: bold; margin-right: 10px;"><?php echo $this->__('Chart Grouping:') ?></label>
                <select id="chart_grouping" name="chart_grouping" class="select" style="padding: 4px 8px; border: 1px solid #ccc;">
                    <?php
                    $currentGrouping = $this->getChartGrouping();
                    foreach ($this->getChartGroupingOptions() as $grouping => $label):
                    ?>
                        <option value="<?php echo $grouping ?>" <?php echo ($currentGrouping === $grouping) ? 'selected="selected"' : '' ?>>
                            <?php echo $label ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div id="chart-container" style="height: 400px; position: relative;">
                <canvas id="purchasesChart" width="800" height="400"></canvas>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Daily Purchases Chart') ?></h4>
        </div>
        <div class="fieldset">
            <p class="note">
                <span><?php echo $this->getDateRangeWarning() ?></span>
            </p>
        </div>
    </div>
<?php endif; ?>

<!-- Order Status Breakdown -->
<?php if ($salesData['total_orders'] > 0): ?>
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Order Status Breakdown') ?></h4>
        </div>
        <div class="fieldset">
            <div class="grid">
                <table cellspacing="0" class="data">
                    <thead>
                        <tr class="headings">
                            <th><?php echo $this->__('Order Status') ?></th>
                            <th><?php echo $this->__('Count') ?></th>
                            <th><?php echo $this->__('Percentage') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $mappedBreakdown = $this->getMappedStatusBreakdown($salesData['status_breakdown']);



                        foreach ($mappedBreakdown as $statusName => $statusData):
                            $percentage = ($statusData['count'] / $salesData['total_orders']) * 100;

                            // Check if this is a mapped status with breakdown data
                            $breakdown = isset($statusData['breakdown']) ? $statusData['breakdown'] : array($statusName => $statusData['count']);
                            // Show breakdown for any mapped status (even single status mappings show the real status name)
                            $hasBreakdown = isset($statusData['breakdown']) && !empty($statusData['breakdown']);
                            $rowId = 'status-row-' . md5($statusName);

                            // Get localized status label for main status
                            $orderConfig = Mage::getSingleton('sales/order_config');
                            $displayStatusName = $statusName;

                            // If this is a single real status (not a custom mapping), get the Magento label
                            if (!$hasBreakdown && isset($statusData['real_statuses']) && count($statusData['real_statuses']) == 1) {
                                $realStatus = $statusData['real_statuses'][0];
                                $magentoLabel = $orderConfig->getStatusLabel($realStatus);
                                if (!empty($magentoLabel)) {
                                    $displayStatusName = $magentoLabel;
                                }
                            }
                        ?>
                            <tr>
                                <td>
                                    <div class="status-pill-container">
                                        <span class="status-pill main-pill" onclick="<?php echo $hasBreakdown ? "toggleStatusBreakdown('{$rowId}')" : '' ?>">
                                            <span class="pill-label"><?php echo htmlspecialchars($displayStatusName) ?></span>
                                            <span class="pill-count"><?php echo number_format($statusData['count']) ?></span>
                                            <?php if ($hasBreakdown): ?>
                                                <span class="pill-dropdown-arrow">▼</span>
                                            <?php endif; ?>
                                        </span>

                                        <?php if ($hasBreakdown): ?>
                                            <div id="<?php echo $rowId ?>-breakdown" class="status-breakdown-dropdown" style="display: none;">
                                                <div class="breakdown-pills">
                                                    <?php foreach ($breakdown as $realStatus => $count):
                                                        // Get localized label for each real status in breakdown
                                                        $realStatusLabel = $orderConfig->getStatusLabel($realStatus);
                                                        if (empty($realStatusLabel)) {
                                                            $realStatusLabel = $realStatus;
                                                        }
                                                    ?>
                                                        <span class="status-pill breakdown-pill">
                                                            <span class="pill-label"><?php echo htmlspecialchars($realStatusLabel) ?></span>
                                                            <span class="pill-count"><?php echo number_format($count) ?></span>
                                                        </span>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td><?php echo number_format($statusData['count']) ?></td>
                                <td><?php echo number_format($percentage, 1) ?>%</td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Top Selling Analytics Dashboard -->
<div class="entry-edit">
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Top Selling Analytics Dashboard') ?></h4>
    </div>
    <div class="fieldset">
        <div class="top-selling-dashboard" style="display: flex; gap: 20px; flex-wrap: wrap;">

            <!-- Column 1: Top Selling Categories -->
            <div class="analytics-column" style="flex: 1; min-width: 300px;">
                <div class="grid">
                    <table cellspacing="0" class="data">
                        <thead>
                            <tr class="headings">
                                <th colspan="3" style="text-align: center; font-size: 14px; padding: 12px;">
                                    <?php echo $this->__('Top Selling Categories') ?>
                                </th>
                            </tr>
                            <tr class="headings">
                                <th><?php echo $this->__('Category Name') ?></th>
                                <th><?php echo $this->__('Quantity Sold') ?></th>
                                <th><?php echo $this->__('Percentage') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $categoriesData = $this->getTopSellingCategoriesData();
                            if (!empty($categoriesData) && !isset($categoriesData['error'])):
                                foreach ($categoriesData as $category):
                                    $isTotal = isset($category['is_total_row']) && $category['is_total_row'];
                                    $rowClass = $isTotal ? 'total-row' : '';
                                    $rowStyle = $isTotal ? 'font-weight: bold; background-color: #f8f8f8; border-top: 2px solid #ddd;' : '';
                            ?>
                                <tr class="<?php echo $rowClass ?>" style="<?php echo $rowStyle ?>">
                                    <td><?php echo htmlspecialchars($category['category_name']) ?></td>
                                    <td><?php echo number_format($category['total_quantity']) ?></td>
                                    <td><?php echo $category['percentage'] ?>%</td>
                                </tr>
                            <?php
                                endforeach;
                            else:
                            ?>
                                <tr>
                                    <td colspan="3" style="text-align: center; padding: 20px; color: #666;">
                                        <?php
                                        if (isset($categoriesData['error'])) {
                                            echo htmlspecialchars($categoriesData['error']);
                                        } else {
                                            echo $this->__('No category data available for the selected date range and filters.');
                                        }
                                        ?>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Column 2: Top Selling Brands -->
            <div class="analytics-column" style="flex: 1; min-width: 300px;">
                <div class="grid">
                    <table cellspacing="0" class="data">
                        <thead>
                            <tr class="headings">
                                <th colspan="3" style="text-align: center; font-size: 14px; padding: 12px;">
                                    <?php echo $this->__('Top Selling Brands') ?>
                                </th>
                            </tr>
                            <tr class="headings">
                                <th><?php echo $this->__('Brand Name') ?></th>
                                <th><?php echo $this->__('Quantity Sold') ?></th>
                                <th><?php echo $this->__('Percentage') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $brandsData = $this->getTopSellingBrandsData();
                            if (!empty($brandsData) && !isset($brandsData['error'])):
                                foreach ($brandsData as $brand):
                                    $isTotal = isset($brand['is_total_row']) && $brand['is_total_row'];
                                    $rowClass = $isTotal ? 'total-row' : '';
                                    $rowStyle = $isTotal ? 'font-weight: bold; background-color: #f8f8f8; border-top: 2px solid #ddd;' : '';
                            ?>
                                <tr class="<?php echo $rowClass ?>" style="<?php echo $rowStyle ?>">
                                    <td><?php echo htmlspecialchars($brand['brand_name']) ?></td>
                                    <td><?php echo number_format($brand['total_quantity']) ?></td>
                                    <td><?php echo $brand['percentage'] ?>%</td>
                                </tr>
                            <?php
                                endforeach;
                            else:
                            ?>
                                <tr>
                                    <td colspan="3" style="text-align: center; padding: 20px; color: #666;">
                                        <?php
                                        if (isset($brandsData['error'])) {
                                            echo htmlspecialchars($brandsData['error']);
                                        } else {
                                            echo $this->__('No brand data available for the selected date range and filters.');
                                        }
                                        ?>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Column 3: Top Selling Products -->
            <div class="analytics-column" style="flex: 1; min-width: 300px;">
                <div class="grid">
                    <table cellspacing="0" class="data">
                        <thead>
                            <tr class="headings">
                                <th colspan="3" style="text-align: center; font-size: 14px; padding: 12px;">
                                    <?php echo $this->__('Top Selling Products') ?>
                                </th>
                            </tr>
                            <tr class="headings">
                                <th><?php echo $this->__('Product') ?></th>
                                <th><?php echo $this->__('Quantity Sold') ?></th>
                                <th><?php echo $this->__('Percentage') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $productsData = $this->getTopSellingProductsData();
                            if (!empty($productsData) && !isset($productsData['error'])):
                                foreach ($productsData as $product):
                                    $isTotal = isset($product['is_total_row']) && $product['is_total_row'];
                                    $rowClass = $isTotal ? 'total-row' : '';
                                    $rowStyle = $isTotal ? 'font-weight: bold; background-color: #f8f8f8; border-top: 2px solid #ddd;' : '';
                            ?>
                                <tr class="<?php echo $rowClass ?>" style="<?php echo $rowStyle ?>">
                                    <td><?php echo htmlspecialchars($product['product_display']) ?></td>
                                    <td><?php echo number_format($product['total_quantity']) ?></td>
                                    <td><?php echo $product['percentage'] ?>%</td>
                                </tr>
                            <?php
                                endforeach;
                            else:
                            ?>
                                <tr>
                                    <td colspan="3" style="text-align: center; padding: 20px; color: #666;">
                                        <?php
                                        if (isset($productsData['error'])) {
                                            echo htmlspecialchars($productsData['error']);
                                        } else {
                                            echo $this->__('No product data available for the selected date range and filters.');
                                        }
                                        ?>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script type="text/javascript">
// Calendar setup
Calendar.setup({
    inputField: 'from_date',
    ifFormat: '%Y-%m-%d',
    button: 'from_date_trig',
    align: 'Bl',
    singleClick: true
});

Calendar.setup({
    inputField: 'to_date',
    ifFormat: '%Y-%m-%d',
    button: 'to_date_trig',
    align: 'Bl',
    singleClick: true
});

// Toggle comparison date fields (no longer needed but keeping for compatibility)
function toggleComparisonDates() {
    // No action needed since we removed custom comparison date fields
}

// Toggle status breakdown display
function toggleStatusBreakdown(rowId) {
    var breakdown = document.getElementById(rowId + '-breakdown');
    var toggleButton = document.querySelector('button[onclick*="' + rowId + '"] .toggle-icon');

    if (breakdown.style.display === 'none' || breakdown.style.display === '') {
        breakdown.style.display = 'block';
        if (toggleButton) toggleButton.innerHTML = '▲';
    } else {
        breakdown.style.display = 'none';
        if (toggleButton) toggleButton.innerHTML = '▼';
    }
}

// Apply quick filter
function applyQuickFilter(filter) {
    var quickFilterDates = <?php echo json_encode(array(
        '30days' => $this->getQuickFilterDates('30days'),
        '90days' => $this->getQuickFilterDates('90days'),
        'ytd' => $this->getQuickFilterDates('ytd'),
        'q1' => $this->getQuickFilterDates('q1'),
        'q2' => $this->getQuickFilterDates('q2'),
        'q3' => $this->getQuickFilterDates('q3'),
        'q4' => $this->getQuickFilterDates('q4')
    )); ?>;

    if (quickFilterDates[filter]) {
        document.getElementById('from_date').value = quickFilterDates[filter].from;
        document.getElementById('to_date').value = quickFilterDates[filter].to;
        document.getElementById('filter_form').submit();
    }
}



// Chart initialization
<?php if ($this->isValidDateRange() && $salesData['total_orders'] > 0): ?>
document.observe('dom:loaded', function() {
    var chartData = <?php echo $this->getChartDataJson() ?>;
    var ctx = document.getElementById('purchasesChart').getContext('2d');

    var datasets = [{
        label: '<?php echo $this->__('Current Period') ?>',
        data: chartData.primary.data,
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1
    }];

    if (chartData.comparison) {
        datasets.push({
            label: '<?php echo $this->__('Comparison Period') ?>',
            data: chartData.comparison.data,
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        });
    }

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.primary.labels,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: '<?php echo $this->__('Daily Purchases Chart') ?>'
                },
                legend: {
                    display: true,
                    position: 'top'
                }
            }
        }
    });
});
<?php endif; ?>
</script>

<style type="text/css">
/* Custom styling for Sales Statistics section only */
.sales-stats-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 20px;
    margin: 10px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.sales-stats-section .grid {
    background: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.sales-stats-section table.data {
    width: 100%;
    border-collapse: collapse;
}

.sales-stats-section table.data thead tr.headings {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
}

.sales-stats-section table.data thead th {
    padding: 15px 12px;
    font-weight: 600;
    text-align: left;
    border: none;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sales-stats-section table.data tbody tr {
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.sales-stats-section table.data tbody tr:hover {
    background-color: #f8f9fa;
}

.sales-stats-section table.data tbody tr:last-child {
    border-bottom: none;
}

.sales-stats-section table.data tbody td {
    padding: 12px;
    vertical-align: middle;
    border: none;
    font-size: 13px;
}

.sales-stats-section table.data tbody td:first-child {
    font-weight: 600;
    color: #495057;
}

.sales-stats-section table.data tbody td:nth-child(2),
.sales-stats-section table.data tbody td:nth-child(3) {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.sales-stats-section .notice {
    color: #28a745;
    font-weight: 600;
    background: rgba(40, 167, 69, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

.sales-stats-section .error {
    color: #dc3545;
    font-weight: 600;
    background: rgba(220, 53, 69, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

.sales-stats-section em {
    background: #ffc107;
    color: #212529;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-style: normal;
    font-weight: 600;
    margin-left: 5px;
}

/* Status breakdown styling */
.status-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.status-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: 2px 6px;
    margin-left: 8px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.status-toggle:hover {
    background-color: #f0f0f0;
}

.toggle-icon {
    font-size: 12px;
    color: #666;
}

.status-breakdown {
    margin-top: 8px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #4a90e2;
}

.status-pills {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.status-pill {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid #90caf9;
    border-radius: 16px;
    padding: 4px 10px;
    font-size: 12px;
    color: #1565c0;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.pill-label {
    font-weight: 500;
    margin-right: 6px;
}

.pill-count {
    background: #1976d2;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-weight: 600;
    font-size: 11px;
    min-width: 18px;
    text-align: center;
}
</style>
