<?php
require_once 'app/Mage.php';
Mage::app();

echo "Testing PFG_PunchHole module...\n";

// Check if module is active
$modules = Mage::getConfig()->getNode('modules')->asArray();
if (isset($modules['PFG_PunchHole'])) {
    echo "Module PFG_PunchHole found in config\n";
    echo "Active: " . ($modules['PFG_PunchHole']['active'] ? 'Yes' : 'No') . "\n";
    echo "Code Pool: " . $modules['PFG_PunchHole']['codePool'] . "\n";
} else {
    echo "Module PFG_PunchHole NOT found in config\n";
}

// Check if controller class exists
if (class_exists('PFG_PunchHole_AjaxController')) {
    echo "Controller class PFG_PunchHole_AjaxController exists\n";
} else {
    echo "Controller class PFG_PunchHole_AjaxController does NOT exist\n";
}

// Check routing configuration
$frontendRouters = Mage::getConfig()->getNode('frontend/routers');
if ($frontendRouters) {
    $routers = $frontendRouters->asArray();
    if (isset($routers['pfg_punchhole'])) {
        echo "Router pfg_punchhole found\n";
        print_r($routers['pfg_punchhole']);
    } else {
        echo "Router pfg_punchhole NOT found\n";
    }
}

echo "Test complete.\n";
?>
