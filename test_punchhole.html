<!DOCTYPE html>
<html>
<head>
    <title>PFG Punch Hole Test</title>
    <script src="http://localhost:8580/skin/frontend/stenik/default/js/pfg-punchhole.js"></script>
</head>
<body>
    <h1>PFG Punch Hole Caching Test</h1>
    
    <h2>Header Customer Section</h2>
    <div class="headerRight" id="header-customer-placeholder">
        <a href="javascript:;" class="compare" title="Compare">
            Compare
            <span class="number" style="display:none;">0</span>
        </a>
        <a href="http://localhost:8580/wishlist/" class="wishlist" title="Wishlist">
            Wishlist
            <span class="number" style="display:none;">0</span>
        </a>
        <a href="http://localhost:8580/customer/account/login/" class="login" title="Login">
            Login
        </a>
    </div>
    
    <h2>Header Cart Section</h2>
    <div id="header-cart-placeholder">
        <a href="http://localhost:8580/checkout/cart/" class="headerCart">
            <span class="number">0</span>
            products
        </a>
    </div>
    
    <h2>Responsive Customer Section</h2>
    <div id="responsive-customer-placeholder">
        <span class="accountLinks">
            <a href="http://localhost:8580/customer/account/login/">Login</a>
            <span class="sep">/</span>
            <a href="http://localhost:8580/customer/account/create/">Registration</a>
        </span>
        <a href="http://localhost:8580/wishlist/" class="wishtlistItems">
            My Wishlist
            <span class="responsiveWishlistitems"> - 0 added</span>
        </a>
        <a href="javascript:;" class="responsiveCompare" title="Compare">
            Compare
            <span class="number" style="display:none;">0</span>
        </a>
    </div>
    
    <h2>Responsive Cart Section</h2>
    <div id="responsive-cart-placeholder">
        <a href="http://localhost:8580/checkout/cart/" class="responsiveCart">
            <span class="responsiveCartitems">0</span>
        </a>
    </div>
    
    <h2>Test Controls</h2>
    <button onclick="testPunchHole()">Initialize PunchHole</button>
    <button onclick="loadAllSections()">Load All Sections (OPTIMIZED - Single AJAX Call)</button>
    <button onclick="testConsolidatedEndpoint()">Test Consolidated Endpoint</button>
    
    <h2>Debug Output</h2>
    <div id="debug-output" style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace;"></div>
    
    <script>
        function log(message) {
            var debugDiv = document.getElementById('debug-output');
            debugDiv.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            console.log(message);
        }
        
        function testPunchHole() {
            log('Testing PunchHole initialization...');
            if (typeof PFGPunchHole !== 'undefined') {
                log('PFGPunchHole is available');
                PFGPunchHole.init('http://localhost:8580/', true); // Enable debug
                log('PunchHole initialized');
            } else {
                log('ERROR: PFGPunchHole is not available');
            }
        }
        
        function loadAllSections() {
            log('Loading all sections...');
            if (typeof PFGPunchHole !== 'undefined') {
                PFGPunchHole.loadAllSections();
                log('All sections loading initiated');
            } else {
                log('ERROR: PFGPunchHole is not available');
            }
        }
        
        function testConsolidatedEndpoint() {
            log('Testing consolidated endpoint directly...');

            fetch('http://localhost:8580/punchhole/ajax/allsections')
                .then(response => response.json())
                .then(data => {
                    log('Consolidated endpoint response:');
                    log(JSON.stringify(data, null, 2));

                    if (data.success) {
                        log('SUCCESS: All sections data received in single request');
                        log('Header Customer: ' + JSON.stringify(data.headerCustomer));
                        log('Header Cart: ' + JSON.stringify(data.headerCart));
                        log('Responsive Customer: ' + JSON.stringify(data.responsiveCustomer));
                        log('Responsive Cart: ' + JSON.stringify(data.responsiveCart));
                    } else {
                        log('ERROR: Consolidated endpoint failed');
                    }
                })
                .catch(error => {
                    log('ERROR: Failed to fetch from consolidated endpoint: ' + error);
                });
        }
        
        // Auto-initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded, checking PFGPunchHole availability...');
            if (typeof PFGPunchHole !== 'undefined') {
                log('PFGPunchHole is available, auto-initializing...');
                testPunchHole();
            } else {
                log('PFGPunchHole not available yet, waiting...');
                setTimeout(function() {
                    if (typeof PFGPunchHole !== 'undefined') {
                        log('PFGPunchHole now available, initializing...');
                        testPunchHole();
                    } else {
                        log('ERROR: PFGPunchHole still not available after timeout');
                    }
                }, 2000);
            }
        });
    </script>
</body>
</html>
