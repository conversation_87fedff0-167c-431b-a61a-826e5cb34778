<!DOCTYPE html>
<html>
<head>
    <title>PFG Punch Hole Test</title>
    <script src="http://localhost:8580/skin/frontend/stenik/default/js/pfg-punchhole.js"></script>
</head>
<body>
    <h1>PFG Punch Hole Caching Test</h1>
    
    <h2>Header Customer Section</h2>
    <div class="headerRight" id="header-customer-placeholder">
        <a href="javascript:;" class="compare" title="Compare">
            Compare
            <span class="number" style="display:none;">0</span>
        </a>
        <a href="http://localhost:8580/wishlist/" class="wishlist" title="Wishlist">
            Wishlist
            <span class="number" style="display:none;">0</span>
        </a>
        <a href="http://localhost:8580/customer/account/login/" class="login" title="Login">
            Login
        </a>
    </div>
    
    <h2>Header Cart Section</h2>
    <div id="header-cart-placeholder">
        <a href="http://localhost:8580/checkout/cart/" class="headerCart">
            <span class="number">0</span>
            products
        </a>
    </div>
    
    <h2>Responsive Customer Section</h2>
    <div id="responsive-customer-placeholder">
        <span class="accountLinks">
            <a href="http://localhost:8580/customer/account/login/">Login</a>
            <span class="sep">/</span>
            <a href="http://localhost:8580/customer/account/create/">Registration</a>
        </span>
        <a href="http://localhost:8580/wishlist/" class="wishtlistItems">
            My Wishlist
            <span class="responsiveWishlistitems"> - 0 added</span>
        </a>
        <a href="javascript:;" class="responsiveCompare" title="Compare">
            Compare
            <span class="number" style="display:none;">0</span>
        </a>
    </div>
    
    <h2>Responsive Cart Section</h2>
    <div id="responsive-cart-placeholder">
        <a href="http://localhost:8580/checkout/cart/" class="responsiveCart">
            <span class="responsiveCartitems">0</span>
        </a>
    </div>
    
    <h2>Test Controls</h2>
    <button onclick="testPunchHole()">Initialize PunchHole</button>
    <button onclick="loadAllSections()">Load All Sections</button>
    <button onclick="testIndividualSections()">Test Individual Sections</button>
    
    <h2>Debug Output</h2>
    <div id="debug-output" style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace;"></div>
    
    <script>
        function log(message) {
            var debugDiv = document.getElementById('debug-output');
            debugDiv.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            console.log(message);
        }
        
        function testPunchHole() {
            log('Testing PunchHole initialization...');
            if (typeof PFGPunchHole !== 'undefined') {
                log('PFGPunchHole is available');
                PFGPunchHole.init('http://localhost:8580/', true); // Enable debug
                log('PunchHole initialized');
            } else {
                log('ERROR: PFGPunchHole is not available');
            }
        }
        
        function loadAllSections() {
            log('Loading all sections...');
            if (typeof PFGPunchHole !== 'undefined') {
                PFGPunchHole.loadAllSections();
                log('All sections loading initiated');
            } else {
                log('ERROR: PFGPunchHole is not available');
            }
        }
        
        function testIndividualSections() {
            log('Testing individual sections...');
            if (typeof PFGPunchHole !== 'undefined') {
                log('Loading headerCustomer...');
                PFGPunchHole.loadSection('headerCustomer');
                
                setTimeout(function() {
                    log('Loading headerCart...');
                    PFGPunchHole.loadSection('headerCart');
                }, 1000);
                
                setTimeout(function() {
                    log('Loading responsiveCustomer...');
                    PFGPunchHole.loadSection('responsiveCustomer');
                }, 2000);
                
                setTimeout(function() {
                    log('Loading responsiveCart...');
                    PFGPunchHole.loadSection('responsiveCart');
                }, 3000);
            } else {
                log('ERROR: PFGPunchHole is not available');
            }
        }
        
        // Auto-initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded, checking PFGPunchHole availability...');
            if (typeof PFGPunchHole !== 'undefined') {
                log('PFGPunchHole is available, auto-initializing...');
                testPunchHole();
            } else {
                log('PFGPunchHole not available yet, waiting...');
                setTimeout(function() {
                    if (typeof PFGPunchHole !== 'undefined') {
                        log('PFGPunchHole now available, initializing...');
                        testPunchHole();
                    } else {
                        log('ERROR: PFGPunchHole still not available after timeout');
                    }
                }, 2000);
            }
        });
    </script>
</body>
</html>
